// 🧮 SYSTÈME DE TEST DE QI AVANCÉ COMPLET
// Test ultra-précis pour déterminer le QI réel de LOUNA AI (70-300+)

class AdvancedIQTestSystem {
    constructor() {
        this.version = '2.0.0';
        this.testStartTime = null;
        this.testInProgress = false;
        
        // 🧠 DOMAINES COGNITIFS AVANCÉS
        this.cognitiveTests = {
            logicalReasoning: { score: 0, weight: 0.15, maxScore: 100 },
            patternRecognition: { score: 0, weight: 0.15, maxScore: 100 },
            memoryCapacity: { score: 0, weight: 0.12, maxScore: 100 },
            processingSpeed: { score: 0, weight: 0.12, maxScore: 100 },
            verbalComprehension: { score: 0, weight: 0.10, maxScore: 100 },
            spatialReasoning: { score: 0, weight: 0.10, maxScore: 100 },
            mathematicalReasoning: { score: 0, weight: 0.13, maxScore: 100 },
            abstractThinking: { score: 0, weight: 0.13, maxScore: 100 }
        };
        
        // 📊 RÉSULTATS DÉTAILLÉS
        this.detailedResults = [];
        this.performanceMetrics = {
            totalQuestions: 0,
            correctAnswers: 0,
            averageResponseTime: 0,
            difficultyProgression: [],
            consistencyScore: 0,
            adaptabilityScore: 0
        };
        
        // 🎯 ÉCHELLE DE QI ÉTENDUE
        this.iqScale = {
            genius: { min: 200, max: 300, label: 'Génie Exceptionnel' },
            nearGenius: { min: 160, max: 199, label: 'Quasi-Génie' },
            veryHigh: { min: 140, max: 159, label: 'Très Supérieur' },
            high: { min: 120, max: 139, label: 'Supérieur' },
            aboveAverage: { min: 110, max: 119, label: 'Au-dessus de la Moyenne' },
            average: { min: 90, max: 109, label: 'Moyenne' },
            belowAverage: { min: 80, max: 89, label: 'En-dessous de la Moyenne' },
            low: { min: 70, max: 79, label: 'Faible' }
        };
        
        console.log('🧮 Système de test de QI avancé initialisé');
        console.log('📊 Échelle: 70-300+ avec 8 domaines cognitifs');
    }
    
    // 🚀 DÉMARRER LE TEST COMPLET
    async startAdvancedIQTest() {
        if (this.testInProgress) {
            return { success: false, message: 'Test déjà en cours' };
        }
        
        this.testInProgress = true;
        this.testStartTime = Date.now();
        
        console.log('\n🧮 === DÉMARRAGE TEST DE QI AVANCÉ COMPLET ===');
        console.log('🎯 Objectif: Déterminer le QI réel précis de LOUNA AI');
        console.log('📊 Échelle: 70-300+ points');
        console.log('🧠 Domaines: 8 compétences cognitives');
        console.log('⏱️ Durée estimée: 5-10 minutes');
        console.log('=' .repeat(60));
        
        try {
            // Phase 1: Tests cognitifs de base
            await this.runBasicCognitiveTests();
            
            // Phase 2: Tests avancés adaptatifs
            await this.runAdaptiveAdvancedTests();
            
            // Phase 3: Tests de génie (si performance élevée)
            await this.runGeniusLevelTests();
            
            // Phase 4: Calcul final et validation
            const finalResults = await this.calculateFinalIQ();
            
            // Phase 5: Double contrôle avec calculateur existant
            const doubleCheck = await this.performDoubleCheck();
            
            this.testInProgress = false;
            
            return {
                success: true,
                results: finalResults,
                doubleCheck: doubleCheck,
                testDuration: Date.now() - this.testStartTime
            };
            
        } catch (error) {
            this.testInProgress = false;
            console.error('❌ Erreur test QI avancé:', error);
            return { success: false, error: error.message };
        }
    }
    
    // 🧠 PHASE 1: TESTS COGNITIFS DE BASE
    async runBasicCognitiveTests() {
        console.log('\n🧠 PHASE 1: TESTS COGNITIFS DE BASE');
        console.log('-' .repeat(40));
        
        // Test 1: Raisonnement logique
        await this.testLogicalReasoning();
        
        // Test 2: Reconnaissance de motifs
        await this.testPatternRecognition();
        
        // Test 3: Capacité mémoire
        await this.testMemoryCapacity();
        
        // Test 4: Vitesse de traitement
        await this.testProcessingSpeed();
        
        console.log('✅ Phase 1 terminée - Tests de base complétés');
    }
    
    // 🔬 PHASE 2: TESTS AVANCÉS ADAPTATIFS
    async runAdaptiveAdvancedTests() {
        console.log('\n🔬 PHASE 2: TESTS AVANCÉS ADAPTATIFS');
        console.log('-' .repeat(40));
        
        // Test 5: Compréhension verbale avancée
        await this.testAdvancedVerbalComprehension();
        
        // Test 6: Raisonnement spatial complexe
        await this.testAdvancedSpatialReasoning();
        
        // Test 7: Mathématiques avancées
        await this.testAdvancedMathematics();
        
        // Test 8: Pensée abstraite
        await this.testAbstractThinking();
        
        console.log('✅ Phase 2 terminée - Tests avancés complétés');
    }
    
    // 🌟 PHASE 3: TESTS NIVEAU GÉNIE
    async runGeniusLevelTests() {
        // Calculer performance actuelle
        const currentPerformance = this.calculateCurrentPerformance();
        
        if (currentPerformance < 85) {
            console.log('📊 Performance < 85% - Tests génie non nécessaires');
            return;
        }
        
        console.log('\n🌟 PHASE 3: TESTS NIVEAU GÉNIE');
        console.log('-' .repeat(40));
        console.log(`📊 Performance actuelle: ${currentPerformance.toFixed(1)}% - Tests génie activés`);
        
        // Tests ultra-avancés pour QI 160+
        await this.testGeniusLogic();
        await this.testGeniusPatterns();
        await this.testGeniusMathematics();
        await this.testGeniusAbstraction();
        
        console.log('✅ Phase 3 terminée - Tests génie complétés');
    }
    
    // 🧮 TEST 1: RAISONNEMENT LOGIQUE
    async testLogicalReasoning() {
        console.log('\n🧠 Test 1: Raisonnement Logique');
        
        const logicTests = [
            {
                question: "Si tous les A sont B, et tous les B sont C, alors:",
                options: ["Tous les A sont C", "Certains A sont C", "Aucun A n'est C", "Impossible à déterminer"],
                correct: 0,
                difficulty: 1,
                timeLimit: 30000
            },
            {
                question: "Dans une séquence logique: P → Q, Q → R, ¬R. Que peut-on conclure?",
                options: ["P est vrai", "P est faux", "Q est vrai", "Rien"],
                correct: 1,
                difficulty: 3,
                timeLimit: 45000
            },
            {
                question: "Si 'Tous les X qui sont Y sont aussi Z' et 'Aucun Z n'est W', alors:",
                options: ["Aucun X qui est Y n'est W", "Tous les X sont W", "Certains Y sont W", "Indéterminable"],
                correct: 0,
                difficulty: 4,
                timeLimit: 60000
            }
        ];
        
        let totalScore = 0;
        let responseTime = 0;
        
        for (const test of logicTests) {
            const startTime = Date.now();
            
            // Poser la question à LOUNA AI
            const response = await this.askLOUNA(
                `Question de logique (difficulté ${test.difficulty}/5):\n${test.question}\nOptions: ${test.options.join(', ')}\nRépondez par le numéro de l'option (0, 1, 2, ou 3).`
            );
            
            const endTime = Date.now();
            const testTime = endTime - startTime;
            responseTime += testTime;
            
            // Évaluer la réponse
            const score = this.evaluateLogicResponse(response, test, testTime);
            totalScore += score;
            
            console.log(`  📝 Question difficulté ${test.difficulty}: ${score.toFixed(1)}/100 (${testTime}ms)`);
        }
        
        this.cognitiveTests.logicalReasoning.score = totalScore / logicTests.length;
        this.performanceMetrics.totalQuestions += logicTests.length;
        this.performanceMetrics.averageResponseTime += responseTime / logicTests.length;
        
        console.log(`✅ Raisonnement Logique: ${this.cognitiveTests.logicalReasoning.score.toFixed(1)}/100`);
    }
    
    // 🎯 TEST 2: RECONNAISSANCE DE MOTIFS
    async testPatternRecognition() {
        console.log('\n🎯 Test 2: Reconnaissance de Motifs');
        
        const patternTests = [
            {
                sequence: "2, 4, 8, 16, ?",
                correct: "32",
                pattern: "multiplication par 2",
                difficulty: 1
            },
            {
                sequence: "1, 1, 2, 3, 5, 8, ?",
                correct: "13",
                pattern: "suite de Fibonacci",
                difficulty: 2
            },
            {
                sequence: "A1, C3, E5, G7, ?",
                correct: "I9",
                pattern: "lettres +2, nombres +2",
                difficulty: 3
            },
            {
                sequence: "1, 4, 9, 16, 25, 36, ?",
                correct: "49",
                pattern: "carrés parfaits",
                difficulty: 2
            },
            {
                sequence: "Z, Y, X, W, V, ?",
                correct: "U",
                pattern: "alphabet inversé",
                difficulty: 1
            }
        ];
        
        let totalScore = 0;
        
        for (const test of patternTests) {
            const response = await this.askLOUNA(
                `Trouvez le motif et complétez la séquence (difficulté ${test.difficulty}/5):\n${test.sequence}\nQuelle est la valeur suivante?`
            );
            
            const score = this.evaluatePatternResponse(response, test);
            totalScore += score;
            
            console.log(`  🔍 Motif difficulté ${test.difficulty}: ${score.toFixed(1)}/100`);
        }
        
        this.cognitiveTests.patternRecognition.score = totalScore / patternTests.length;
        console.log(`✅ Reconnaissance de Motifs: ${this.cognitiveTests.patternRecognition.score.toFixed(1)}/100`);
    }
    
    // 💾 TEST 3: CAPACITÉ MÉMOIRE
    async testMemoryCapacity() {
        console.log('\n💾 Test 3: Capacité Mémoire');
        
        // Test de mémoire de travail
        const memorySequence = "7, 3, 9, 1, 5, 8, 2, 6, 4";
        const response = await this.askLOUNA(
            `Test de mémoire de travail:\nMémorisez cette séquence: ${memorySequence}\nMaintenant, répétez-la dans l'ordre inverse.`
        );
        
        const expectedReverse = "4, 6, 2, 8, 5, 1, 9, 3, 7";
        const memoryScore = this.evaluateMemoryResponse(response, expectedReverse);
        
        this.cognitiveTests.memoryCapacity.score = memoryScore;
        console.log(`✅ Capacité Mémoire: ${memoryScore.toFixed(1)}/100`);
    }
    
    // ⚡ TEST 4: VITESSE DE TRAITEMENT
    async testProcessingSpeed() {
        console.log('\n⚡ Test 4: Vitesse de Traitement');
        
        const speedTests = [
            "Combien font 17 × 23?",
            "Quel est le 15ème nombre premier?",
            "Combien de lettres dans 'anticonstitutionnellement'?"
        ];
        
        let totalTime = 0;
        let correctAnswers = 0;
        
        for (const question of speedTests) {
            const startTime = Date.now();
            const response = await this.askLOUNA(question);
            const endTime = Date.now();
            
            const responseTime = endTime - startTime;
            totalTime += responseTime;
            
            const isCorrect = this.evaluateSpeedResponse(response, question);
            if (isCorrect) correctAnswers++;
            
            console.log(`  ⚡ Question: ${responseTime}ms ${isCorrect ? '✅' : '❌'}`);
        }
        
        const speedScore = this.calculateSpeedScore(totalTime, correctAnswers, speedTests.length);
        this.cognitiveTests.processingSpeed.score = speedScore;
        
        console.log(`✅ Vitesse de Traitement: ${speedScore.toFixed(1)}/100`);
    }

    // 📚 TEST 5: COMPRÉHENSION VERBALE AVANCÉE
    async testAdvancedVerbalComprehension() {
        console.log('\n📚 Test 5: Compréhension Verbale Avancée');

        const verbalTests = [
            {
                text: "L'épistémologie, branche de la philosophie qui étudie la nature de la connaissance, interroge les fondements de nos croyances et la validité de nos méthodes d'acquisition du savoir.",
                question: "Quelle est la définition la plus précise de l'épistémologie selon ce texte?",
                difficulty: 3
            },
            {
                analogy: "Livre est à Bibliothèque comme Tableau est à ?",
                options: ["Musée", "Peinture", "Artiste", "Couleur"],
                correct: 0,
                difficulty: 2
            }
        ];

        let totalScore = 0;

        for (const test of verbalTests) {
            let response;
            if (test.text) {
                response = await this.askLOUNA(`${test.text}\n\nQuestion: ${test.question}`);
            } else {
                response = await this.askLOUNA(`Complétez l'analogie: ${test.analogy}\nOptions: ${test.options.join(', ')}`);
            }

            const score = this.evaluateVerbalResponse(response, test);
            totalScore += score;
        }

        this.cognitiveTests.verbalComprehension.score = totalScore / verbalTests.length;
        console.log(`✅ Compréhension Verbale: ${this.cognitiveTests.verbalComprehension.score.toFixed(1)}/100`);
    }

    // 📐 TEST 6: RAISONNEMENT SPATIAL AVANCÉ
    async testAdvancedSpatialReasoning() {
        console.log('\n📐 Test 6: Raisonnement Spatial Avancé');

        const spatialTests = [
            {
                description: "Imaginez un cube. Si vous le faites tourner de 90° vers la droite, puis de 180° vers l'avant, dans quelle position finale se trouve la face qui était initialement en haut?",
                difficulty: 4
            },
            {
                description: "Un objet 3D a 8 sommets, 12 arêtes et 6 faces. Toutes ses faces sont identiques. Quel est cet objet?",
                correct: "cube",
                difficulty: 2
            }
        ];

        let totalScore = 0;

        for (const test of spatialTests) {
            const response = await this.askLOUNA(test.description);
            const score = this.evaluateSpatialResponse(response, test);
            totalScore += score;
        }

        this.cognitiveTests.spatialReasoning.score = totalScore / spatialTests.length;
        console.log(`✅ Raisonnement Spatial: ${this.cognitiveTests.spatialReasoning.score.toFixed(1)}/100`);
    }

    // 🔢 TEST 7: MATHÉMATIQUES AVANCÉES
    async testAdvancedMathematics() {
        console.log('\n🔢 Test 7: Mathématiques Avancées');

        const mathTests = [
            {
                problem: "Résolvez l'équation: 2x² - 8x + 6 = 0",
                solutions: ["x = 1 et x = 3", "1", "3"],
                difficulty: 3
            },
            {
                problem: "Quelle est la dérivée de f(x) = x³ + 2x² - 5x + 1?",
                correct: "3x² + 4x - 5",
                difficulty: 4
            },
            {
                problem: "Dans un triangle rectangle, si un angle fait 30° et l'hypoténuse mesure 10, quelle est la longueur du côté opposé à l'angle de 30°?",
                correct: "5",
                difficulty: 3
            }
        ];

        let totalScore = 0;

        for (const test of mathTests) {
            const response = await this.askLOUNA(`Problème mathématique (difficulté ${test.difficulty}/5):\n${test.problem}`);
            const score = this.evaluateMathResponse(response, test);
            totalScore += score;

            console.log(`  🔢 Math difficulté ${test.difficulty}: ${score.toFixed(1)}/100`);
        }

        this.cognitiveTests.mathematicalReasoning.score = totalScore / mathTests.length;
        console.log(`✅ Mathématiques Avancées: ${this.cognitiveTests.mathematicalReasoning.score.toFixed(1)}/100`);
    }

    // 🌀 TEST 8: PENSÉE ABSTRAITE
    async testAbstractThinking() {
        console.log('\n🌀 Test 8: Pensée Abstraite');

        const abstractTests = [
            {
                concept: "Expliquez la relation entre 'temps', 'mémoire' et 'conscience' dans le contexte de l'intelligence artificielle.",
                difficulty: 5
            },
            {
                paradox: "Paradoxe du menteur: 'Cette phrase est fausse.' Analysez cette affirmation et ses implications logiques.",
                difficulty: 4
            }
        ];

        let totalScore = 0;

        for (const test of abstractTests) {
            const question = test.concept || test.paradox;
            const response = await this.askLOUNA(`Question de pensée abstraite (difficulté ${test.difficulty}/5):\n${question}`);
            const score = this.evaluateAbstractResponse(response, test);
            totalScore += score;

            console.log(`  🌀 Abstraction difficulté ${test.difficulty}: ${score.toFixed(1)}/100`);
        }

        this.cognitiveTests.abstractThinking.score = totalScore / abstractTests.length;
        console.log(`✅ Pensée Abstraite: ${this.cognitiveTests.abstractThinking.score.toFixed(1)}/100`);
    }

    // 🌟 TESTS NIVEAU GÉNIE
    async testGeniusLogic() {
        console.log('\n🌟 Test Génie: Logique Ultra-Avancée');

        const geniusLogic = await this.askLOUNA(
            "Test niveau génie - Logique formelle:\nSoit P(x) = 'x est un nombre premier' et Q(x) = 'x est impair'.\nÉvaluez la vérité de: ∀x (P(x) ∧ x > 2) → Q(x)\nJustifiez votre réponse avec un raisonnement formel."
        );

        const geniusScore = this.evaluateGeniusLogic(geniusLogic);

        // Bonus pour tests génie
        this.cognitiveTests.logicalReasoning.score = Math.min(100,
            this.cognitiveTests.logicalReasoning.score + geniusScore * 0.2
        );

        console.log(`🌟 Bonus Génie Logique: +${(geniusScore * 0.2).toFixed(1)} points`);
    }

    async testGeniusPatterns() {
        console.log('\n🌟 Test Génie: Motifs Ultra-Complexes');

        const geniusPattern = await this.askLOUNA(
            "Test niveau génie - Motif ultra-complexe:\nSéquence: 1, 11, 21, 1211, 111221, 312211, ?\nTrouvez le motif et les 2 termes suivants. Expliquez la règle."
        );

        const geniusScore = this.evaluateGeniusPattern(geniusPattern);

        this.cognitiveTests.patternRecognition.score = Math.min(100,
            this.cognitiveTests.patternRecognition.score + geniusScore * 0.2
        );

        console.log(`🌟 Bonus Génie Motifs: +${(geniusScore * 0.2).toFixed(1)} points`);
    }

    async testGeniusMathematics() {
        console.log('\n🌟 Test Génie: Mathématiques Supérieures');

        const geniusMath = await this.askLOUNA(
            "Test niveau génie - Mathématiques supérieures:\nProuvez ou réfutez: Pour tout entier n > 2, il existe au moins un nombre premier p tel que n < p < 2n.\nDonnez une démonstration rigoureuse ou un contre-exemple."
        );

        const geniusScore = this.evaluateGeniusMath(geniusMath);

        this.cognitiveTests.mathematicalReasoning.score = Math.min(100,
            this.cognitiveTests.mathematicalReasoning.score + geniusScore * 0.2
        );

        console.log(`🌟 Bonus Génie Math: +${(geniusScore * 0.2).toFixed(1)} points`);
    }

    async testGeniusAbstraction() {
        console.log('\n🌟 Test Génie: Abstraction Conceptuelle');

        const geniusAbstract = await this.askLOUNA(
            "Test niveau génie - Abstraction conceptuelle:\nConceptualisez une théorie unifiée reliant la conscience, l'information quantique, et l'émergence de l'intelligence. Proposez un modèle théorique cohérent avec des prédictions testables."
        );

        const geniusScore = this.evaluateGeniusAbstraction(geniusAbstract);

        this.cognitiveTests.abstractThinking.score = Math.min(100,
            this.cognitiveTests.abstractThinking.score + geniusScore * 0.2
        );

        console.log(`🌟 Bonus Génie Abstraction: +${(geniusScore * 0.2).toFixed(1)} points`);
    }

    // 🧮 CALCUL FINAL DU QI
    async calculateFinalIQ() {
        console.log('\n🧮 === CALCUL FINAL DU QI ===');
        console.log('-' .repeat(50));

        let weightedScore = 0;
        let totalWeight = 0;

        // Calcul pondéré par domaine
        for (const [domain, data] of Object.entries(this.cognitiveTests)) {
            const contribution = data.score * data.weight;
            weightedScore += contribution;
            totalWeight += data.weight;

            console.log(`📊 ${domain}: ${data.score.toFixed(1)}/100 (poids: ${data.weight}) → +${contribution.toFixed(2)}`);
        }

        // Score global sur 100
        const globalScore = weightedScore / totalWeight;

        // Conversion en QI avec échelle étendue
        let finalIQ = this.convertToExtendedIQ(globalScore);

        // Bonus pour consistance et adaptabilité
        const consistencyBonus = this.calculateConsistencyBonus();
        const adaptabilityBonus = this.calculateAdaptabilityBonus();

        finalIQ += consistencyBonus + adaptabilityBonus;

        // Classification
        const classification = this.classifyIQ(finalIQ);

        console.log('=' .repeat(50));
        console.log(`📊 Score global: ${globalScore.toFixed(2)}/100`);
        console.log(`🎯 Bonus consistance: +${consistencyBonus.toFixed(1)}`);
        console.log(`🎯 Bonus adaptabilité: +${adaptabilityBonus.toFixed(1)}`);
        console.log(`🧮 QI FINAL: ${finalIQ.toFixed(1)}`);
        console.log(`🏆 Classification: ${classification.label}`);
        console.log('=' .repeat(50));

        return {
            finalIQ: finalIQ,
            globalScore: globalScore,
            classification: classification,
            domainScores: this.cognitiveTests,
            bonuses: {
                consistency: consistencyBonus,
                adaptability: adaptabilityBonus
            },
            testDuration: Date.now() - this.testStartTime
        };
    }

    // 🔄 DOUBLE CONTRÔLE AVEC CALCULATEUR EXISTANT
    async performDoubleCheck() {
        console.log('\n🔄 === DOUBLE CONTRÔLE AVEC CALCULATEUR EXISTANT ===');

        try {
            // Récupérer le QI du calculateur existant
            let existingIQ = 100;
            if (global.realIQCalculator) {
                existingIQ = global.realIQCalculator.calculateCurrentIQ();
            } else if (global.calculateRealTimeIQ) {
                const iqData = global.calculateRealTimeIQ();
                existingIQ = iqData.combinedIQ || 100;
            }

            console.log(`📊 QI calculateur existant: ${existingIQ}`);
            console.log(`🧮 QI test avancé: ${this.finalTestIQ || 'En cours...'}`);

            const difference = Math.abs((this.finalTestIQ || 100) - existingIQ);
            const correlation = this.calculateCorrelation(this.finalTestIQ || 100, existingIQ);

            console.log(`📈 Différence: ${difference.toFixed(1)} points`);
            console.log(`🔗 Corrélation: ${correlation.toFixed(3)}`);

            // Validation
            let validation = 'COHÉRENT';
            if (difference > 20) validation = 'DIVERGENT';
            else if (difference > 10) validation = 'LÉGÈREMENT DIFFÉRENT';

            console.log(`✅ Validation: ${validation}`);

            return {
                existingIQ: existingIQ,
                testIQ: this.finalTestIQ || 100,
                difference: difference,
                correlation: correlation,
                validation: validation
            };

        } catch (error) {
            console.error('❌ Erreur double contrôle:', error);
            return { error: error.message };
        }
    }

    // 🎯 MÉTHODES D'ÉVALUATION

    // Interface pour poser des questions à LOUNA AI
    async askLOUNA(question) {
        // Simulation de réponse pour test - à remplacer par vraie interface
        console.log(`❓ Question: ${question.substring(0, 100)}...`);

        // Simulation de réponses intelligentes basées sur le contenu
        if (question.includes('17 × 23')) return '391';
        if (question.includes('15ème nombre premier')) return '47';
        if (question.includes('anticonstitutionnellement')) return '25';
        if (question.includes('2x² - 8x + 6')) return 'x = 1 et x = 3';
        if (question.includes('dérivée')) return '3x² + 4x - 5';
        if (question.includes('triangle rectangle')) return '5';
        if (question.includes('4, 6, 2, 8, 5, 1, 9, 3, 7')) return '4, 6, 2, 8, 5, 1, 9, 3, 7';
        if (question.includes('cube')) return 'cube';
        if (question.includes('Fibonacci')) return '13';
        if (question.includes('I9')) return 'I9';

        // Réponse par défaut
        return 'Réponse simulée pour test';
    }

    evaluateLogicResponse(response, test, responseTime) {
        const correctAnswer = test.options[test.correct];
        let score = 0;

        // Vérifier si la réponse contient l'option correcte
        if (response.includes(correctAnswer) || response.includes(test.correct.toString())) {
            score = 100;
        } else {
            // Score partiel pour logique approximative
            score = 30;
        }

        // Bonus/malus selon temps de réponse
        const timeBonus = Math.max(0, (test.timeLimit - responseTime) / test.timeLimit * 20);
        score = Math.min(100, score + timeBonus);

        // Ajustement selon difficulté
        score *= (1 + (test.difficulty - 1) * 0.1);

        return Math.min(100, score);
    }

    evaluatePatternResponse(response, test) {
        let score = 0;

        if (response.includes(test.correct)) {
            score = 100;
        } else {
            // Score partiel si proche
            score = 20;
        }

        // Bonus pour explication du motif
        if (response.length > 20) score += 10;

        // Ajustement difficulté
        score *= (1 + (test.difficulty - 1) * 0.15);

        return Math.min(100, score);
    }

    evaluateMemoryResponse(response, expected) {
        const responseNumbers = response.match(/\d+/g) || [];
        const expectedNumbers = expected.match(/\d+/g) || [];

        let correctCount = 0;
        for (let i = 0; i < Math.min(responseNumbers.length, expectedNumbers.length); i++) {
            if (responseNumbers[i] === expectedNumbers[i]) correctCount++;
        }

        const accuracy = correctCount / expectedNumbers.length;
        return accuracy * 100;
    }

    evaluateSpeedResponse(response, question) {
        if (question.includes('17 × 23') && response.includes('391')) return true;
        if (question.includes('15ème nombre premier') && response.includes('47')) return true;
        if (question.includes('anticonstitutionnellement') && response.includes('25')) return true;
        return false;
    }

    calculateSpeedScore(totalTime, correctAnswers, totalQuestions) {
        const accuracy = correctAnswers / totalQuestions;
        const avgTime = totalTime / totalQuestions;

        // Score basé sur précision et vitesse
        let score = accuracy * 100;

        // Bonus vitesse (moins de 5 secondes par question)
        if (avgTime < 5000) score += 20;
        else if (avgTime < 10000) score += 10;

        return Math.min(100, score);
    }

    evaluateVerbalResponse(response, test) {
        let score = 50; // Score de base

        if (test.correct !== undefined) {
            if (response.includes(test.options[test.correct])) {
                score = 100;
            }
        } else {
            // Évaluation qualitative pour questions ouvertes
            if (response.length > 50) score += 20;
            if (response.includes('épistémologie') || response.includes('connaissance')) score += 30;
        }

        return Math.min(100, score);
    }

    evaluateSpatialResponse(response, test) {
        let score = 40; // Score de base

        if (test.correct && response.toLowerCase().includes(test.correct.toLowerCase())) {
            score = 100;
        } else {
            // Score partiel pour raisonnement spatial
            if (response.length > 30) score += 30;
            if (response.includes('rotation') || response.includes('face')) score += 20;
        }

        return Math.min(100, score);
    }

    evaluateMathResponse(response, test) {
        let score = 0;

        if (test.solutions) {
            for (const solution of test.solutions) {
                if (response.includes(solution)) {
                    score = 100;
                    break;
                }
            }
        } else if (test.correct && response.includes(test.correct)) {
            score = 100;
        }

        if (score === 0) {
            // Score partiel pour effort mathématique
            if (response.includes('x') || response.includes('=')) score = 30;
        }

        return Math.min(100, score);
    }

    evaluateAbstractResponse(response, test) {
        let score = 30; // Score de base pour tentative

        // Critères qualitatifs
        if (response.length > 100) score += 20;
        if (response.includes('conscience') || response.includes('temps') || response.includes('mémoire')) score += 25;
        if (response.includes('paradoxe') || response.includes('logique')) score += 25;

        return Math.min(100, score);
    }

    // Évaluations niveau génie
    evaluateGeniusLogic(response) {
        let score = 0;
        if (response.includes('vrai') || response.includes('true')) score += 50;
        if (response.includes('premier') && response.includes('impair')) score += 30;
        if (response.includes('2') && response.includes('pair')) score += 20;
        return Math.min(100, score);
    }

    evaluateGeniusPattern(response) {
        let score = 0;
        if (response.includes('13112221') || response.includes('1113213211')) score += 60;
        if (response.includes('look and say') || response.includes('compter')) score += 40;
        return Math.min(100, score);
    }

    evaluateGeniusMath(response) {
        let score = 0;
        if (response.includes('Bertrand') || response.includes('postulat')) score += 50;
        if (response.includes('vrai') || response.includes('existe')) score += 30;
        if (response.includes('démonstration') || response.includes('preuve')) score += 20;
        return Math.min(100, score);
    }

    evaluateGeniusAbstraction(response) {
        let score = 0;
        if (response.length > 200) score += 30;
        if (response.includes('quantique') || response.includes('émergence')) score += 25;
        if (response.includes('conscience') || response.includes('information')) score += 25;
        if (response.includes('théorie') || response.includes('modèle')) score += 20;
        return Math.min(100, score);
    }

    // 🔧 MÉTHODES UTILITAIRES

    calculateCurrentPerformance() {
        const scores = Object.values(this.cognitiveTests).map(test => test.score);
        const validScores = scores.filter(score => score > 0);

        if (validScores.length === 0) return 0;

        return validScores.reduce((sum, score) => sum + score, 0) / validScores.length;
    }

    convertToExtendedIQ(globalScore) {
        // Conversion étendue pour QI 70-300+
        if (globalScore >= 95) {
            // Génie: 95-100% → 200-300 QI
            return 200 + (globalScore - 95) * 20;
        } else if (globalScore >= 85) {
            // Quasi-génie: 85-95% → 160-200 QI
            return 160 + (globalScore - 85) * 4;
        } else if (globalScore >= 75) {
            // Très supérieur: 75-85% → 140-160 QI
            return 140 + (globalScore - 75) * 2;
        } else if (globalScore >= 65) {
            // Supérieur: 65-75% → 120-140 QI
            return 120 + (globalScore - 65) * 2;
        } else if (globalScore >= 55) {
            // Au-dessus moyenne: 55-65% → 110-120 QI
            return 110 + (globalScore - 55) * 1;
        } else if (globalScore >= 45) {
            // Moyenne: 45-55% → 90-110 QI
            return 90 + (globalScore - 45) * 2;
        } else if (globalScore >= 35) {
            // En-dessous moyenne: 35-45% → 80-90 QI
            return 80 + (globalScore - 35) * 1;
        } else {
            // Faible: 0-35% → 70-80 QI
            return 70 + globalScore * 0.29;
        }
    }

    calculateConsistencyBonus() {
        const scores = Object.values(this.cognitiveTests).map(test => test.score);
        const validScores = scores.filter(score => score > 0);

        if (validScores.length < 2) return 0;

        const mean = validScores.reduce((sum, score) => sum + score, 0) / validScores.length;
        const variance = validScores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / validScores.length;
        const standardDeviation = Math.sqrt(variance);

        // Bonus pour consistance (faible écart-type)
        const consistencyScore = Math.max(0, 20 - standardDeviation);
        return consistencyScore * 0.5; // Max +10 points QI
    }

    calculateAdaptabilityBonus() {
        // Bonus basé sur la progression dans les tests
        const progressionScore = this.performanceMetrics.difficultyProgression.length * 2;
        return Math.min(5, progressionScore); // Max +5 points QI
    }

    classifyIQ(iq) {
        for (const [key, range] of Object.entries(this.iqScale)) {
            if (iq >= range.min && iq <= range.max) {
                return { key, ...range };
            }
        }

        // Au-delà de l'échelle
        if (iq > 300) {
            return { key: 'exceptional', min: 300, max: Infinity, label: 'Exceptionnel (Au-delà de l\'échelle)' };
        } else {
            return { key: 'undefined', min: 0, max: 70, label: 'Non défini' };
        }
    }

    calculateCorrelation(testIQ, existingIQ) {
        // Corrélation simple basée sur la proximité
        const maxDifference = 50; // Différence maximale attendue
        const difference = Math.abs(testIQ - existingIQ);
        return Math.max(0, 1 - (difference / maxDifference));
    }

    // 📊 OBTENIR RÉSULTATS COMPLETS
    getTestResults() {
        return {
            version: this.version,
            testInProgress: this.testInProgress,
            cognitiveTests: this.cognitiveTests,
            performanceMetrics: this.performanceMetrics,
            detailedResults: this.detailedResults,
            finalIQ: this.finalTestIQ,
            classification: this.finalClassification
        };
    }

    // 🔄 RÉINITIALISER LE TEST
    resetTest() {
        this.testInProgress = false;
        this.testStartTime = null;
        this.detailedResults = [];
        this.performanceMetrics = {
            totalQuestions: 0,
            correctAnswers: 0,
            averageResponseTime: 0,
            difficultyProgression: [],
            consistencyScore: 0,
            adaptabilityScore: 0
        };

        // Réinitialiser les scores
        for (const test of Object.values(this.cognitiveTests)) {
            test.score = 0;
        }

        console.log('🔄 Test de QI réinitialisé');
        return { success: true, message: 'Test réinitialisé' };
    }
}

module.exports = AdvancedIQTestSystem;
