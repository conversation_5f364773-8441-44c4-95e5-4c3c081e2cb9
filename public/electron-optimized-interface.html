<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI - Interface Electron Optimisée</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        .main-container {
            display: grid;
            grid-template-columns: 300px 1fr 300px;
            grid-template-rows: 60px 1fr 50px;
            height: 100vh;
            gap: 10px;
            padding: 10px;
        }

        .header {
            grid-column: 1 / -1;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            backdrop-filter: blur(10px);
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #ff6b9d;
        }

        .status-indicators {
            display: flex;
            gap: 15px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4ade80;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .left-panel {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            overflow-y: auto;
        }

        .right-panel {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            overflow-y: auto;
        }

        .center-panel {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            max-height: calc(100vh - 300px);
        }

        .message {
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 10px;
            max-width: 80%;
        }

        .message.user {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin-left: auto;
            text-align: right;
        }

        .message.ai {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            margin-right: auto;
        }

        .chat-input-container {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .chat-input {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
            outline: none;
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .send-button {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: scale(1.05);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .panel-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #ff6b9d;
            text-align: center;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
        }

        .stat-value {
            font-weight: bold;
            color: #4ade80;
        }

        .memory-entry {
            background: rgba(255, 255, 255, 0.05);
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .memory-type {
            color: #ff6b9d;
            font-weight: bold;
            font-size: 10px;
            text-transform: uppercase;
        }

        .footer {
            grid-column: 1 / -1;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #ff6b9d;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .quality-indicator {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: bold;
            margin-left: 8px;
        }

        .quality-excellent { background: #4ade80; color: #000; }
        .quality-good { background: #fbbf24; color: #000; }
        .quality-average { background: #f97316; color: #fff; }
        .quality-poor { background: #ef4444; color: #fff; }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header">
            <div class="logo">🧠 LOUNA AI - Electron</div>
            <div class="status-indicators">
                <div class="status-indicator">
                    <div class="status-dot" id="deepseek-status"></div>
                    <span>DeepSeek R1 8B</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot" id="thermal-status"></div>
                    <span>Mémoire Thermique</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot" id="electron-status"></div>
                    <span>Electron IPC</span>
                </div>
            </div>
        </div>

        <div class="left-panel">
            <div class="panel-title">📊 Statistiques IA</div>
            <div id="ai-stats">
                <div class="stat-item">
                    <span>QI Agent:</span>
                    <span class="stat-value" id="agent-iq">100</span>
                </div>
                <div class="stat-item">
                    <span>QI Mémoire:</span>
                    <span class="stat-value" id="memory-iq">0</span>
                </div>
                <div class="stat-item">
                    <span>QI Combiné:</span>
                    <span class="stat-value" id="combined-iq">100</span>
                </div>
                <div class="stat-item">
                    <span>Neurones:</span>
                    <span class="stat-value" id="neurons">240</span>
                </div>
                <div class="stat-item">
                    <span>Température:</span>
                    <span class="stat-value" id="temperature">37.0°C</span>
                </div>
                <div class="stat-item">
                    <span>Efficacité:</span>
                    <span class="stat-value" id="efficiency">95%</span>
                </div>
            </div>
        </div>

        <div class="center-panel">
            <div class="chat-container">
                <div class="chat-messages" id="chat-messages">
                    <div class="message ai">
                        🧠 Bonjour ! Je suis LOUNA AI avec DeepSeek R1 8B intégré directement dans Electron. 
                        Ma mémoire thermique fonctionne en temps réel et j'apprends de chaque interaction. 
                        Comment puis-je vous aider aujourd'hui ?
                    </div>
                </div>
                <div class="chat-input-container">
                    <input type="text" class="chat-input" id="chat-input" 
                           placeholder="Tapez votre message... (Entrée pour envoyer)">
                    <button class="send-button" id="send-button">Envoyer</button>
                </div>
            </div>
        </div>

        <div class="right-panel">
            <div class="panel-title">🧠 Mémoire Récente</div>
            <div id="recent-memories">
                <!-- Les mémoires récentes seront affichées ici -->
            </div>
        </div>

        <div class="footer">
            🚀 LOUNA AI v3.0 - Interface Electron Optimisée - Systèmes IA intégrés
        </div>
    </div>

    <script>
        // 🤖 INTERFACE ELECTRON AVEC IPC
        const { ipcRenderer } = require('electron');
        
        let isProcessing = false;
        
        // Éléments DOM
        const chatMessages = document.getElementById('chat-messages');
        const chatInput = document.getElementById('chat-input');
        const sendButton = document.getElementById('send-button');
        
        // 📡 FONCTION D'ENVOI DE MESSAGE
        async function sendMessage() {
            const message = chatInput.value.trim();
            if (!message || isProcessing) return;
            
            isProcessing = true;
            sendButton.disabled = true;
            sendButton.innerHTML = '<div class="loading"></div>';
            
            // Afficher le message utilisateur
            addMessage(message, 'user');
            chatInput.value = '';
            
            try {
                // Envoyer via IPC à DeepSeek
                const result = await ipcRenderer.invoke('deepseek-chat', message);
                
                if (result.success) {
                    // Afficher la réponse avec indicateur de qualité
                    const qualityClass = getQualityClass(result.quality.score);
                    const qualityText = result.quality.grade;
                    
                    addMessage(
                        result.response + 
                        `<span class="quality-indicator ${qualityClass}">${qualityText} ${result.quality.score}/100</span>`,
                        'ai'
                    );
                    
                    // Mettre à jour les stats
                    updateStats();
                    
                } else {
                    addMessage(`❌ Erreur: ${result.error}`, 'ai');
                }
                
            } catch (error) {
                console.error('Erreur chat:', error);
                addMessage(`❌ Erreur de communication: ${error.message}`, 'ai');
            }
            
            isProcessing = false;
            sendButton.disabled = false;
            sendButton.textContent = 'Envoyer';
        }
        
        // 💬 AJOUTER UN MESSAGE AU CHAT
        function addMessage(content, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = content;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // 🎨 CLASSE DE QUALITÉ
        function getQualityClass(score) {
            if (score >= 80) return 'quality-excellent';
            if (score >= 60) return 'quality-good';
            if (score >= 40) return 'quality-average';
            return 'quality-poor';
        }
        
        // 📊 METTRE À JOUR LES STATISTIQUES
        async function updateStats() {
            try {
                // 🚀 RÉCUPÉRER LES NEURONES EN TEMPS RÉEL DU SERVEUR HTTP
                let realTimeNeurons = 152000; // Valeur par défaut
                try {
                    const serverResponse = await fetch('/api/metrics');
                    const serverData = await serverResponse.json();
                    if (serverData.success && serverData.neurons) {
                        realTimeNeurons = serverData.neurons;
                        console.log(`🧠 NEURONES INTERFACE TEMPS RÉEL: ${realTimeNeurons} neurones du serveur`);
                    }
                } catch (fetchError) {
                    console.log('⚠️ Impossible de récupérer les neurones du serveur, utilisation d\'Electron');
                }

                const result = await ipcRenderer.invoke('get-ai-stats');

                if (result.success && result.stats) {
                    const { deepseek, thermalMemory } = result.stats;

                    if (deepseek) {
                        document.getElementById('agent-iq').textContent = '100';
                    }

                    if (thermalMemory) {
                        document.getElementById('memory-iq').textContent = thermalMemory.memoryIQ || '0';
                        document.getElementById('combined-iq').textContent = (100 + (thermalMemory.memoryIQ || 0));
                        // 🧠 UTILISER LES NEURONES EN TEMPS RÉEL DU SERVEUR
                        document.getElementById('neurons').textContent = realTimeNeurons;
                        document.getElementById('temperature').textContent = `${thermalMemory.temperature || 37.0}°C`;
                        document.getElementById('efficiency').textContent = `${thermalMemory.efficiency || 95}%`;
                    }
                }

            } catch (error) {
                console.error('Erreur mise à jour stats:', error);
            }
        }
        
        // 🎯 ÉVÉNEMENTS
        sendButton.addEventListener('click', sendMessage);
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // 🔄 INITIALISATION
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 Interface Electron optimisée chargée');
            updateStats();
            
            // Mettre à jour les stats toutes les 5 secondes
            setInterval(updateStats, 5000);
        });
    </script>
</body>
</html>
