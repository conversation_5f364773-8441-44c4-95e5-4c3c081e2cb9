{"timestamp": 1749481270951, "metrics": {"system": {"cpu": {"usage": 0.106, "temperature": 37, "cores": 10}, "memory": {"used": 21293888, "total": 17179869184, "efficiency": 5.752300126903549}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749480970948}, "performance": {"uptime": 298.271, "efficiency": 63.9, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 99, "connections": 0}}, "history": [{"timestamp": 1749481071089, "cpu": 0.0455, "memory": 0.11239992454648018, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749481073089, "cpu": 0.0123, "memory": 0.11315983720123768, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749481075091, "cpu": 0.0102, "memory": 0.11388198472559452, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749481077092, "cpu": 0.0114, "memory": 0.11133616790175438, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749481079093, "cpu": 0.009899999999999999, "memory": 0.11187302879989147, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749481081095, "cpu": 1.2958, "memory": 0.11363718658685684, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749481083095, "cpu": 0.0075, "memory": 0.11423728428781033, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749481085096, "cpu": 0.0366, "memory": 0.11078733950853348, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749481087096, "cpu": 0.009000000000000001, "memory": 0.11236653663218021, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749481089097, "cpu": 0.0065, "memory": 0.11297944001853466, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749481091098, "cpu": 0.6668000000000001, "memory": 0.11455821804702282, "temperature": 37, "efficiency": 65}, {"timestamp": 1749481093099, "cpu": 0.06849999999999999, "memory": 0.11513880454003811, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749481095100, "cpu": 0.0136, "memory": 0.11168350465595722, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749481097101, "cpu": 0.0259, "memory": 0.11316714808344841, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749481099102, "cpu": 0.013, "memory": 0.11375546455383301, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749481101103, "cpu": 0.034499999999999996, "memory": 0.115201435983181, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749481103103, "cpu": 0.16620000000000001, "memory": 0.11586793698370457, "temperature": 37, "efficiency": 63.3}, {"timestamp": 1749481105104, "cpu": 0.012199999999999999, "memory": 0.112595921382308, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749481107104, "cpu": 0.0167, "memory": 0.1142334658652544, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749481109108, "cpu": 0.0197, "memory": 0.11477065272629261, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749481111107, "cpu": 0.0809, "memory": 0.11620186269283295, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749481113109, "cpu": 0.0131, "memory": 0.11273934505879879, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749481115110, "cpu": 0.0063999999999999994, "memory": 0.11329906992614269, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749481117111, "cpu": 0.0126, "memory": 0.11482383124530315, "temperature": 37, "efficiency": 64}, {"timestamp": 1749481119112, "cpu": 0.0112, "memory": 0.11537722311913967, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749481121114, "cpu": 0.6244000000000001, "memory": 0.11292467825114727, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749481123114, "cpu": 0.0121, "memory": 0.11356864124536514, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749481125115, "cpu": 0.012, "memory": 0.11421381495893002, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749481127116, "cpu": 0.0107, "memory": 0.11567086912691593, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749481129117, "cpu": 0.0336, "memory": 0.11616875417530537, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749481131117, "cpu": 0.0847, "memory": 0.11394540779292583, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749481133119, "cpu": 0.0121, "memory": 0.1146114431321621, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749481135120, "cpu": 0.0138, "memory": 0.11514364741742611, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749481137121, "cpu": 0.0127, "memory": 0.11665569618344307, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749481139122, "cpu": 0.0102, "memory": 0.11719646863639355, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749481141123, "cpu": 1.2368000000000001, "memory": 0.11421917006373405, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749481143123, "cpu": 0.0104, "memory": 0.11502150446176529, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749481145125, "cpu": 0.0252, "memory": 0.11566691100597382, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749481147126, "cpu": 0.0178, "memory": 0.11717984452843666, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749481149127, "cpu": 0.014899999999999998, "memory": 0.11778227053582668, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749481151133, "cpu": 1.3537000000000001, "memory": 0.11500772088766098, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749481153129, "cpu": 0.0121, "memory": 0.11574025265872478, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749481155131, "cpu": 0.0226, "memory": 0.11631110683083534, "temperature": 37, "efficiency": 64}, {"timestamp": 1749481157178, "cpu": 0.054299999999999994, "memory": 0.11802772060036659, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749481159155, "cpu": 0.0362, "memory": 0.11852607131004333, "temperature": 37, "efficiency": 63.4}, {"timestamp": 1749481161156, "cpu": 0.6133000000000001, "memory": 0.11601760052144527, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749481163156, "cpu": 0.011000000000000001, "memory": 0.11665970087051392, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749481165158, "cpu": 0.0172, "memory": 0.11752280406653881, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749481167157, "cpu": 0.0095, "memory": 0.11906097643077374, "temperature": 37, "efficiency": 63.3}, {"timestamp": 1749481169159, "cpu": 0.0151, "memory": 0.11558900587260723, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749481171159, "cpu": 0.0441, "memory": 0.1170092262327671, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749481173161, "cpu": 0.021900000000000003, "memory": 0.11757398024201393, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749481175162, "cpu": 0.028200000000000003, "memory": 0.11813705787062645, "temperature": 37, "efficiency": 65}, {"timestamp": 1749481177163, "cpu": 0.009000000000000001, "memory": 0.11557843536138535, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749481179164, "cpu": 0.0067, "memory": 0.11617694981396198, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749481181164, "cpu": 0.0319, "memory": 0.11803898960351944, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749481183165, "cpu": 0.0063, "memory": 0.1189808826893568, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749481185166, "cpu": 0.0125, "memory": 0.11961539275944233, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749481187167, "cpu": 0.0085, "memory": 0.11730510741472244, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749481189168, "cpu": 0.0077, "memory": 0.11779908090829849, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749481191168, "cpu": 0.053899999999999997, "memory": 0.11925231665372849, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749481193169, "cpu": 0.029, "memory": 0.11980850249528885, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749481195170, "cpu": 0.015, "memory": 0.12047993950545788, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749481197171, "cpu": 0.009000000000000001, "memory": 0.1179402694106102, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749481199184, "cpu": 0.0259, "memory": 0.11846558190882206, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749481201184, "cpu": 0.0437, "memory": 0.11995146051049232, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749481203185, "cpu": 0.0136, "memory": 0.12087030336260796, "temperature": 37, "efficiency": 64}, {"timestamp": 1749481205193, "cpu": 0.0177, "memory": 0.12154774740338326, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749481207188, "cpu": 0.0327, "memory": 0.11894600465893745, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749481209189, "cpu": 0.012199999999999999, "memory": 0.1194788608700037, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749481211190, "cpu": 0.0375, "memory": 0.12098499573767185, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749481213190, "cpu": 0.0205, "memory": 0.12155445292592049, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749481215192, "cpu": 0.0895, "memory": 0.11813286691904068, "temperature": 37, "efficiency": 65}, {"timestamp": 1749481217193, "cpu": 0.0063, "memory": 0.11961678974330425, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749481219193, "cpu": 0.0135, "memory": 0.1201131846755743, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749481221195, "cpu": 0.09709999999999999, "memory": 0.12156735174357891, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749481223196, "cpu": 0.0068, "memory": 0.12212824076414108, "temperature": 37, "efficiency": 64}, {"timestamp": 1749481225197, "cpu": 0.0225, "memory": 0.11872788891196251, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749481227197, "cpu": 0.0075, "memory": 0.12035844847559929, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749481229197, "cpu": 0.0071, "memory": 0.12095593847334385, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749481231197, "cpu": 0.06570000000000001, "memory": 0.12270207516849041, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749481233199, "cpu": 0.0664, "memory": 0.11920235119760036, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749481235200, "cpu": 0.014799999999999999, "memory": 0.11977897956967354, "temperature": 37, "efficiency": 65}, {"timestamp": 1749481237202, "cpu": 0.018699999999999998, "memory": 0.12125791981816292, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749481239202, "cpu": 0.06620000000000001, "memory": 0.1216439064592123, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749481241203, "cpu": 0.24009999999999998, "memory": 0.12330948375165462, "temperature": 37, "efficiency": 64}, {"timestamp": 1749481243203, "cpu": 0.1217, "memory": 0.11978563852608204, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749481245204, "cpu": 0.0279, "memory": 0.12035192921757698, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749481247205, "cpu": 0.0441, "memory": 0.12183692306280136, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749481249205, "cpu": 0.0421, "memory": 0.12233112938702106, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749481251206, "cpu": 0.612, "memory": 0.11984845623373985, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749481253209, "cpu": 0.0853, "memory": 0.1204135362058878, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749481255210, "cpu": 0.0651, "memory": 0.12108972296118736, "temperature": 37, "efficiency": 66}, {"timestamp": 1749481257240, "cpu": 0.218, "memory": 0.12297695502638817, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749481259214, "cpu": 0.0352, "memory": 0.1235730480402708, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749481261215, "cpu": 0.5909, "memory": 0.1207815483212471, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749481263216, "cpu": 0.0828, "memory": 0.12134681455790997, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749481265217, "cpu": 0.0545, "memory": 0.12191357091069221, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749481267218, "cpu": 0.0674, "memory": 0.12334892526268959, "temperature": 37, "efficiency": 64}, {"timestamp": 1749481269219, "cpu": 0.106, "memory": 0.12394674122333527, "temperature": 37, "efficiency": 63.9}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}