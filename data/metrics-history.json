{"timestamp": 1749476565586, "metrics": {"system": {"cpu": {"usage": 0.006200000000000001, "temperature": 37, "cores": 10}, "memory": {"used": 21013720, "total": 17179869184, "efficiency": 7.127032324855179}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749475965584}, "performance": {"uptime": 598.594, "efficiency": 64.4, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749476366090, "cpu": 0.0229, "memory": 0.0952456146478653, "temperature": 37, "efficiency": 70}, {"timestamp": 1749476368092, "cpu": 0.0172, "memory": 0.09342348203063011, "temperature": 37, "efficiency": 70.5}, {"timestamp": 1749476370093, "cpu": 0.0204, "memory": 0.09628427214920521, "temperature": 37, "efficiency": 69.8}, {"timestamp": 1749476372095, "cpu": 0.1252, "memory": 0.09373389184474945, "temperature": 37, "efficiency": 70.4}, {"timestamp": 1749476374097, "cpu": 0.1609, "memory": 0.09589111432433128, "temperature": 37, "efficiency": 69.8}, {"timestamp": 1749476376096, "cpu": 0.053200000000000004, "memory": 0.09452425874769688, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749476378101, "cpu": 0.1857, "memory": 0.09683705866336823, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749476380102, "cpu": 0.0195, "memory": 0.09499811567366123, "temperature": 37, "efficiency": 71.3}, {"timestamp": 1749476382101, "cpu": 0.0104, "memory": 0.09748660959303379, "temperature": 37, "efficiency": 70.7}, {"timestamp": 1749476384102, "cpu": 0.2053, "memory": 0.09556589648127556, "temperature": 37, "efficiency": 69.9}, {"timestamp": 1749476386103, "cpu": 0.0101, "memory": 0.09792265482246876, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749476388103, "cpu": 0.0088, "memory": 0.09635207243263721, "temperature": 37, "efficiency": 69.8}, {"timestamp": 1749476390103, "cpu": 0.11180000000000001, "memory": 0.09835460223257542, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749476392103, "cpu": 0.0075, "memory": 0.09581707417964935, "temperature": 37, "efficiency": 69.9}, {"timestamp": 1749476394102, "cpu": 0.1559, "memory": 0.09852210059762001, "temperature": 37, "efficiency": 69.1}, {"timestamp": 1749476396103, "cpu": 0.0095, "memory": 0.09634401649236679, "temperature": 37, "efficiency": 69.8}, {"timestamp": 1749476398105, "cpu": 0.0126, "memory": 0.09821676649153233, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749476400104, "cpu": 0.0197, "memory": 0.0970668625086546, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749476402105, "cpu": 0.9206000000000001, "memory": 0.09889742359519005, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749476404104, "cpu": 0.018799999999999997, "memory": 0.10086614638566971, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749476406105, "cpu": 0.0136, "memory": 0.09958101436495781, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749476408104, "cpu": 0.2613, "memory": 0.10151378810405731, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749476410106, "cpu": 0.009600000000000001, "memory": 0.09971712715923786, "temperature": 37, "efficiency": 70.1}, {"timestamp": 1749476412107, "cpu": 0.0131, "memory": 0.10200687684118748, "temperature": 37, "efficiency": 69.5}, {"timestamp": 1749476414107, "cpu": 0.2708, "memory": 0.09984606876969337, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749476416109, "cpu": 0.0075, "memory": 0.10218690149486065, "temperature": 37, "efficiency": 68.2}, {"timestamp": 1749476418110, "cpu": 0.0097, "memory": 0.10075815953314304, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749476420111, "cpu": 0.2925, "memory": 0.10277326218783855, "temperature": 37, "efficiency": 68}, {"timestamp": 1749476422111, "cpu": 0.0084, "memory": 0.10036453604698181, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749476424113, "cpu": 0.21220000000000003, "memory": 0.10305899195373058, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749476426113, "cpu": 0.1941, "memory": 0.10115723125636578, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749476428114, "cpu": 0.0089, "memory": 0.10316702537238598, "temperature": 37, "efficiency": 68}, {"timestamp": 1749476430116, "cpu": 0.0212, "memory": 0.10182349942624569, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749476432117, "cpu": 0.209, "memory": 0.1031387597322464, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749476434118, "cpu": 0.1451, "memory": 0.10507483966648579, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749476436119, "cpu": 0.0111, "memory": 0.10424242354929447, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749476438119, "cpu": 0.32989999999999997, "memory": 0.10616322979331017, "temperature": 37, "efficiency": 67}, {"timestamp": 1749476440120, "cpu": 0.0165, "memory": 0.10452172718942165, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749476442121, "cpu": 0.0166, "memory": 0.10664109140634537, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749476444122, "cpu": 0.4354, "memory": 0.10437574237585068, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749476446123, "cpu": 0.0114, "memory": 0.10652639903128147, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749476448124, "cpu": 0.0154, "memory": 0.10536122135818005, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749476450124, "cpu": 0.6905, "memory": 0.10723709128797054, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749476452125, "cpu": 0.0138, "memory": 0.10859835892915726, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749476454126, "cpu": 0.1729, "memory": 0.10719527490437031, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749476456127, "cpu": 0.5884, "memory": 0.10929470881819725, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749476458128, "cpu": 0.0113, "memory": 0.10721227154135704, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749476460129, "cpu": 0.0093, "memory": 0.10968642309308052, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749476462129, "cpu": 0.0273, "memory": 0.10792268440127373, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749476464128, "cpu": 0.0085, "memory": 0.10986924171447754, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749476466130, "cpu": 0.0157, "memory": 0.10820496827363968, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749476468131, "cpu": 0.1361, "memory": 0.11013131588697433, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749476470132, "cpu": 0.014100000000000001, "memory": 0.10791178792715073, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749476472133, "cpu": 0.0084, "memory": 0.10996926575899124, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749476474134, "cpu": 0.0118, "memory": 0.1089652068912983, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749476476135, "cpu": 0.097, "memory": 0.1095243263989687, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749476478135, "cpu": 0.0088, "memory": 0.112541439011693, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749476480137, "cpu": 0.0109, "memory": 0.1111301127821207, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749476482138, "cpu": 0.0086, "memory": 0.11169007048010826, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749476484139, "cpu": 0.2395, "memory": 0.11027287691831589, "temperature": 37, "efficiency": 66}, {"timestamp": 1749476486140, "cpu": 0.08499999999999999, "memory": 0.11299923062324524, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749476488140, "cpu": 0.017499999999999998, "memory": 0.11048903688788414, "temperature": 37, "efficiency": 66}, {"timestamp": 1749476490142, "cpu": 0.0143, "memory": 0.11302190832793713, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749476492143, "cpu": 0.0286, "memory": 0.11498043313622475, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749476494144, "cpu": 0.014899999999999998, "memory": 0.11316072195768356, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749476496145, "cpu": 0.0115, "memory": 0.11528744362294674, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749476498147, "cpu": 0.0125, "memory": 0.11413879692554474, "temperature": 37, "efficiency": 65}, {"timestamp": 1749476500148, "cpu": 0.01, "memory": 0.11532492935657501, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749476502149, "cpu": 0.0147, "memory": 0.11361115612089634, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749476504149, "cpu": 0.2154, "memory": 0.11543845757842064, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749476506150, "cpu": 0.0073999999999999995, "memory": 0.11395583860576153, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749476508151, "cpu": 0.0073, "memory": 0.11655059643089771, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749476510153, "cpu": 0.1489, "memory": 0.11460622772574425, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749476512154, "cpu": 0.0127, "memory": 0.11586467735469341, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749476514155, "cpu": 0.1804, "memory": 0.11450513266026974, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749476516155, "cpu": 0.17650000000000002, "memory": 0.11645597405731678, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749476518158, "cpu": 0.015899999999999997, "memory": 0.11825757101178169, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749476520159, "cpu": 0.0138, "memory": 0.11702040210366249, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749476522160, "cpu": 0.0071, "memory": 0.11893738992512226, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749476524160, "cpu": 0.1576, "memory": 0.11650659143924713, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749476526161, "cpu": 0.009899999999999999, "memory": 0.1190603245049715, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749476528162, "cpu": 0.0063999999999999994, "memory": 0.11775754392147064, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749476530163, "cpu": 0.0135, "memory": 0.11886539869010448, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749476532163, "cpu": 0.0077, "memory": 0.12071956880390644, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749476534164, "cpu": 0.013200000000000002, "memory": 0.1201435923576355, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749476536164, "cpu": 0.01, "memory": 0.12066615745425224, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749476538165, "cpu": 0.0118, "memory": 0.11921543627977371, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749476540167, "cpu": 0.009899999999999999, "memory": 0.12171166017651558, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749476542168, "cpu": 0.0077, "memory": 0.11856299825012684, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749476544169, "cpu": 0.006999999999999999, "memory": 0.12184130027890205, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749476546169, "cpu": 0.0093, "memory": 0.1197662204504013, "temperature": 37, "efficiency": 66}, {"timestamp": 1749476548170, "cpu": 0.0087, "memory": 0.1209908165037632, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749476550171, "cpu": 0.006600000000000001, "memory": 0.1196401659399271, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749476552174, "cpu": 0.15, "memory": 0.12089749798178673, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749476554174, "cpu": 0.1552, "memory": 0.12274342589080334, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749476556174, "cpu": 0.01, "memory": 0.12137237936258316, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749476558176, "cpu": 0.2516, "memory": 0.12246854603290558, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749476560177, "cpu": 0.0107, "memory": 0.1210664864629507, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749476562178, "cpu": 0.0107, "memory": 0.12292908504605293, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749476564178, "cpu": 0.006200000000000001, "memory": 0.12231594882905483, "temperature": 37, "efficiency": 64.4}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}