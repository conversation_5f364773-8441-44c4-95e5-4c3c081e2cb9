{"timestamp": 1749480042982, "metrics": {"system": {"cpu": {"usage": 0.0125, "temperature": 37, "cores": 10}, "memory": {"used": 16939224, "total": 17179869184, "efficiency": 20.653247613679966}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749479442978}, "performance": {"uptime": 598.764, "efficiency": 68.9, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749479843653, "cpu": 0.009600000000000001, "memory": 0.11319932527840137, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749479845655, "cpu": 0.1405, "memory": 0.07731909863650799, "temperature": 37, "efficiency": 74.3}, {"timestamp": 1749479847655, "cpu": 0.0077, "memory": 0.07988568395376205, "temperature": 37, "efficiency": 73.6}, {"timestamp": 1749479849657, "cpu": 0.1961, "memory": 0.08140378631651402, "temperature": 37, "efficiency": 73.2}, {"timestamp": 1749479851657, "cpu": 0.20170000000000002, "memory": 0.07871962152421474, "temperature": 37, "efficiency": 73.9}, {"timestamp": 1749479853658, "cpu": 0.0077, "memory": 0.08092704229056835, "temperature": 37, "efficiency": 73.4}, {"timestamp": 1749479855659, "cpu": 0.0083, "memory": 0.07927720434963703, "temperature": 37, "efficiency": 74.8}, {"timestamp": 1749479857661, "cpu": 0.22529999999999997, "memory": 0.07965415716171265, "temperature": 37, "efficiency": 74.7}, {"timestamp": 1749479859662, "cpu": 0.20179999999999998, "memory": 0.0809231773018837, "temperature": 37, "efficiency": 74.3}, {"timestamp": 1749479861662, "cpu": 0.0063, "memory": 0.08300542831420898, "temperature": 37, "efficiency": 73.9}, {"timestamp": 1749479863663, "cpu": 0.0857, "memory": 0.08012736216187477, "temperature": 37, "efficiency": 73.5}, {"timestamp": 1749479865665, "cpu": 0.8210000000000001, "memory": 0.0822985079139471, "temperature": 37, "efficiency": 72.7}, {"timestamp": 1749479867666, "cpu": 0.009000000000000001, "memory": 0.08064634166657925, "temperature": 37, "efficiency": 73.4}, {"timestamp": 1749479869666, "cpu": 0.2926, "memory": 0.08166548795998096, "temperature": 37, "efficiency": 73.1}, {"timestamp": 1749479871667, "cpu": 0.0081, "memory": 0.08400804363191128, "temperature": 37, "efficiency": 72.5}, {"timestamp": 1749479873668, "cpu": 0.012400000000000001, "memory": 0.08162627927958965, "temperature": 37, "efficiency": 73.2}, {"timestamp": 1749479875669, "cpu": 0.24849999999999997, "memory": 0.0830349512398243, "temperature": 37, "efficiency": 72.7}, {"timestamp": 1749479877671, "cpu": 0.0105, "memory": 0.08091414347290993, "temperature": 37, "efficiency": 73.4}, {"timestamp": 1749479879671, "cpu": 0.1428, "memory": 0.08270740509033203, "temperature": 37, "efficiency": 72.8}, {"timestamp": 1749479881672, "cpu": 0.1144, "memory": 0.08424101397395134, "temperature": 37, "efficiency": 72.4}, {"timestamp": 1749479883673, "cpu": 0.0125, "memory": 0.0821798574179411, "temperature": 37, "efficiency": 73}, {"timestamp": 1749479885674, "cpu": 0.0107, "memory": 0.08465764112770557, "temperature": 37, "efficiency": 72.3}, {"timestamp": 1749479887676, "cpu": 0.1516, "memory": 0.08518025279045105, "temperature": 37, "efficiency": 72.1}, {"timestamp": 1749479889677, "cpu": 0.2101, "memory": 0.08252500556409359, "temperature": 37, "efficiency": 72.9}, {"timestamp": 1749479891678, "cpu": 0.0092, "memory": 0.08471929468214512, "temperature": 37, "efficiency": 72.3}, {"timestamp": 1749479893678, "cpu": 0.2218, "memory": 0.08550593629479408, "temperature": 37, "efficiency": 72}, {"timestamp": 1749479895679, "cpu": 0.012899999999999998, "memory": 0.08377223275601864, "temperature": 37, "efficiency": 73.7}, {"timestamp": 1749479897679, "cpu": 0.006999999999999999, "memory": 0.0854266807436943, "temperature": 37, "efficiency": 73.2}, {"timestamp": 1749479899681, "cpu": 0.5917, "memory": 0.0864972360432148, "temperature": 37, "efficiency": 72.8}, {"timestamp": 1749479901681, "cpu": 0.0073999999999999995, "memory": 0.0851514283567667, "temperature": 37, "efficiency": 72.2}, {"timestamp": 1749479903681, "cpu": 0.007600000000000001, "memory": 0.08684382773935795, "temperature": 37, "efficiency": 71.7}, {"timestamp": 1749479905684, "cpu": 0.1469, "memory": 0.084339315071702, "temperature": 37, "efficiency": 72.4}, {"timestamp": 1749479907685, "cpu": 0.0065, "memory": 0.08651595562696457, "temperature": 37, "efficiency": 71.8}, {"timestamp": 1749479909685, "cpu": 0.3034, "memory": 0.08805086836218834, "temperature": 37, "efficiency": 71.3}, {"timestamp": 1749479911687, "cpu": 0.1583, "memory": 0.08587688207626343, "temperature": 37, "efficiency": 72}, {"timestamp": 1749479913688, "cpu": 0.0092, "memory": 0.08756476454436779, "temperature": 37, "efficiency": 71.5}, {"timestamp": 1749479915688, "cpu": 0.0104, "memory": 0.08547506295144558, "temperature": 37, "efficiency": 72.1}, {"timestamp": 1749479917689, "cpu": 0.16119999999999998, "memory": 0.08635255508124828, "temperature": 37, "efficiency": 71.8}, {"timestamp": 1749479919689, "cpu": 0.22260000000000002, "memory": 0.0878713559359312, "temperature": 37, "efficiency": 71.4}, {"timestamp": 1749479921690, "cpu": 0.0147, "memory": 0.08642063476145267, "temperature": 37, "efficiency": 71.9}, {"timestamp": 1749479923691, "cpu": 0.12279999999999999, "memory": 0.08733547292649746, "temperature": 37, "efficiency": 71.6}, {"timestamp": 1749479925692, "cpu": 0.011000000000000001, "memory": 0.08967453613877296, "temperature": 37, "efficiency": 71}, {"timestamp": 1749479927693, "cpu": 0.0143, "memory": 0.08709551766514778, "temperature": 37, "efficiency": 71.7}, {"timestamp": 1749479929694, "cpu": 0.2676, "memory": 0.08852677419781685, "temperature": 37, "efficiency": 71.2}, {"timestamp": 1749479931696, "cpu": 0.0123, "memory": 0.0909710768610239, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749479933696, "cpu": 0.0119, "memory": 0.08905334398150444, "temperature": 37, "efficiency": 72.3}, {"timestamp": 1749479935698, "cpu": 0.2463, "memory": 0.09043612517416477, "temperature": 37, "efficiency": 71.9}, {"timestamp": 1749479937699, "cpu": 0.013300000000000001, "memory": 0.08780867792665958, "temperature": 37, "efficiency": 71.5}, {"timestamp": 1749479939700, "cpu": 0.1862, "memory": 0.08916785009205341, "temperature": 37, "efficiency": 71.1}, {"timestamp": 1749479941702, "cpu": 0.1484, "memory": 0.09073358960449696, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749479943703, "cpu": 0.0082, "memory": 0.08864984847605228, "temperature": 37, "efficiency": 71.3}, {"timestamp": 1749479945704, "cpu": 0.014100000000000001, "memory": 0.09094621054828167, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749479947705, "cpu": 0.2682, "memory": 0.09132879786193371, "temperature": 37, "efficiency": 70.4}, {"timestamp": 1749479949707, "cpu": 0.2873, "memory": 0.09286864660680294, "temperature": 37, "efficiency": 70.3}, {"timestamp": 1749479951707, "cpu": 0.012199999999999999, "memory": 0.09138998575508595, "temperature": 37, "efficiency": 70.8}, {"timestamp": 1749479953708, "cpu": 0.1483, "memory": 0.0922253355383873, "temperature": 37, "efficiency": 70.5}, {"timestamp": 1749479955710, "cpu": 0.0138, "memory": 0.09083347395062447, "temperature": 37, "efficiency": 71}, {"timestamp": 1749479957710, "cpu": 0.0068, "memory": 0.09209965355694294, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749479959712, "cpu": 0.32139999999999996, "memory": 0.09312676265835762, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749479961711, "cpu": 0.0082, "memory": 0.09101331233978271, "temperature": 37, "efficiency": 70.9}, {"timestamp": 1749479963712, "cpu": 0.0063999999999999994, "memory": 0.09297579526901245, "temperature": 37, "efficiency": 70.4}, {"timestamp": 1749479965713, "cpu": 0.1083, "memory": 0.09445887990295887, "temperature": 37, "efficiency": 70}, {"timestamp": 1749479967715, "cpu": 0.0113, "memory": 0.0913436058908701, "temperature": 37, "efficiency": 70.8}, {"timestamp": 1749479969715, "cpu": 0.17700000000000002, "memory": 0.0934454146772623, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749479971717, "cpu": 0.1621, "memory": 0.09508593939244747, "temperature": 37, "efficiency": 69.8}, {"timestamp": 1749479973717, "cpu": 0.0119, "memory": 0.09227446280419827, "temperature": 37, "efficiency": 71.7}, {"timestamp": 1749479975718, "cpu": 0.0102, "memory": 0.09455508552491665, "temperature": 37, "efficiency": 71.2}, {"timestamp": 1749479977717, "cpu": 0.233, "memory": 0.0952018890529871, "temperature": 37, "efficiency": 70.9}, {"timestamp": 1749479979719, "cpu": 0.23969999999999997, "memory": 0.09299255907535553, "temperature": 37, "efficiency": 70.3}, {"timestamp": 1749479981719, "cpu": 0.0115, "memory": 0.0959964469075203, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749479983720, "cpu": 0.0962, "memory": 0.09275339543819427, "temperature": 37, "efficiency": 70.4}, {"timestamp": 1749479985721, "cpu": 0.0069, "memory": 0.09526452049612999, "temperature": 37, "efficiency": 69.8}, {"timestamp": 1749479987722, "cpu": 0.009899999999999999, "memory": 0.09277663193643093, "temperature": 37, "efficiency": 70.4}, {"timestamp": 1749479989723, "cpu": 0.1855, "memory": 0.09349850006401539, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749479991725, "cpu": 0.0131, "memory": 0.09571313858032227, "temperature": 37, "efficiency": 69.7}, {"timestamp": 1749479993726, "cpu": 0.0161, "memory": 0.09322683326900005, "temperature": 37, "efficiency": 70.3}, {"timestamp": 1749479995727, "cpu": 0.1814, "memory": 0.09460099972784519, "temperature": 37, "efficiency": 69.9}, {"timestamp": 1749479997728, "cpu": 0.0072, "memory": 0.0963795930147171, "temperature": 37, "efficiency": 69.5}, {"timestamp": 1749479999729, "cpu": 0.1374, "memory": 0.09409673511981964, "temperature": 37, "efficiency": 70}, {"timestamp": 1749480001730, "cpu": 0.18439999999999998, "memory": 0.09560594335198402, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749480003731, "cpu": 0.0069, "memory": 0.09761126711964607, "temperature": 37, "efficiency": 69.1}, {"timestamp": 1749480005731, "cpu": 0.0089, "memory": 0.09586992673575878, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749480007733, "cpu": 0.0977, "memory": 0.09663645178079605, "temperature": 37, "efficiency": 69.4}, {"timestamp": 1749480009732, "cpu": 0.1352, "memory": 0.09843874722719193, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749480011733, "cpu": 0.0116, "memory": 0.09648227132856846, "temperature": 37, "efficiency": 70.7}, {"timestamp": 1749480013733, "cpu": 0.2609, "memory": 0.09828326292335987, "temperature": 37, "efficiency": 70.1}, {"timestamp": 1749480015734, "cpu": 0.0097, "memory": 0.09632757864892483, "temperature": 37, "efficiency": 69.5}, {"timestamp": 1749480017735, "cpu": 0.013200000000000002, "memory": 0.09815776720643044, "temperature": 37, "efficiency": 69}, {"timestamp": 1749480019735, "cpu": 0.1382, "memory": 0.09973100386559963, "temperature": 37, "efficiency": 68.5}, {"timestamp": 1749480021735, "cpu": 0.0093, "memory": 0.09707342833280563, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749480023736, "cpu": 0.0111, "memory": 0.09867222979664803, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749480025737, "cpu": 0.004699999999999999, "memory": 0.09726248681545258, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749480027738, "cpu": 0.0071, "memory": 0.0979722011834383, "temperature": 37, "efficiency": 69.1}, {"timestamp": 1749480029738, "cpu": 0.0049, "memory": 0.09777150116860867, "temperature": 37, "efficiency": 69.1}, {"timestamp": 1749480031739, "cpu": 0.006999999999999999, "memory": 0.09928070940077305, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749480033740, "cpu": 0.006600000000000001, "memory": 0.09995521977543831, "temperature": 37, "efficiency": 68.5}, {"timestamp": 1749480035740, "cpu": 0.006200000000000001, "memory": 0.09832060895860195, "temperature": 37, "efficiency": 69}, {"timestamp": 1749480037740, "cpu": 0.012199999999999999, "memory": 0.09969295933842659, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749480039741, "cpu": 0.0086, "memory": 0.10111378505825996, "temperature": 37, "efficiency": 68.2}, {"timestamp": 1749480041742, "cpu": 0.0125, "memory": 0.0985992606729269, "temperature": 37, "efficiency": 68.9}], "alerts": [], "optimizations": [{"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}