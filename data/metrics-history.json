{"timestamp": 1749480642967, "metrics": {"system": {"cpu": {"usage": 0.045399999999999996, "temperature": 37, "cores": 10}, "memory": {"used": 20764024, "total": 17179869184, "efficiency": 11.064179002192986}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749479442978}, "performance": {"uptime": 1199.433, "efficiency": 65.7, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749480444241, "cpu": 0.0072, "memory": 0.09826282039284706, "temperature": 37, "efficiency": 71.2}, {"timestamp": 1749480446242, "cpu": 0.0083, "memory": 0.10070372372865677, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749480448242, "cpu": 0.009000000000000001, "memory": 0.10118191130459309, "temperature": 37, "efficiency": 70.5}, {"timestamp": 1749480450242, "cpu": 0.009399999999999999, "memory": 0.1002325676381588, "temperature": 37, "efficiency": 70.7}, {"timestamp": 1749480452243, "cpu": 0.0201, "memory": 0.1016432885080576, "temperature": 37, "efficiency": 70.4}, {"timestamp": 1749480454244, "cpu": 0.0077, "memory": 0.10259640403091908, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749480456244, "cpu": 0.0063999999999999994, "memory": 0.10091932490468025, "temperature": 37, "efficiency": 71.6}, {"timestamp": 1749480458246, "cpu": 0.0112, "memory": 0.10233232751488686, "temperature": 37, "efficiency": 71.3}, {"timestamp": 1749480460247, "cpu": 0.006999999999999999, "memory": 0.09950431995093822, "temperature": 37, "efficiency": 70.9}, {"timestamp": 1749480462248, "cpu": 0.006, "memory": 0.10082479566335678, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749480464249, "cpu": 0.0065, "memory": 0.10221544653177261, "temperature": 37, "efficiency": 70.3}, {"timestamp": 1749480466250, "cpu": 0.0414, "memory": 0.10050516575574875, "temperature": 37, "efficiency": 70.7}, {"timestamp": 1749480468250, "cpu": 0.0073999999999999995, "memory": 0.10196384973824024, "temperature": 37, "efficiency": 70.3}, {"timestamp": 1749480470251, "cpu": 0.0102, "memory": 0.10454277507960796, "temperature": 37, "efficiency": 69.7}, {"timestamp": 1749480472251, "cpu": 0.008, "memory": 0.10126200504601002, "temperature": 37, "efficiency": 70.5}, {"timestamp": 1749480474252, "cpu": 0.007899999999999999, "memory": 0.10318122804164886, "temperature": 37, "efficiency": 70}, {"timestamp": 1749480476253, "cpu": 0.008, "memory": 0.10136812925338745, "temperature": 37, "efficiency": 70.5}, {"timestamp": 1749480478253, "cpu": 0.0486, "memory": 0.1020187046378851, "temperature": 37, "efficiency": 70.3}, {"timestamp": 1749480480255, "cpu": 0.0126, "memory": 0.10442892089486122, "temperature": 37, "efficiency": 69.7}, {"timestamp": 1749480482256, "cpu": 0.009600000000000001, "memory": 0.10164063423871994, "temperature": 37, "efficiency": 70.4}, {"timestamp": 1749480484257, "cpu": 0.012199999999999999, "memory": 0.10245423763990402, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749480486258, "cpu": 0.0086, "memory": 0.10544299148023129, "temperature": 37, "efficiency": 69.5}, {"timestamp": 1749480488259, "cpu": 0.0088, "memory": 0.1029876060783863, "temperature": 37, "efficiency": 70.1}, {"timestamp": 1749480490260, "cpu": 0.01, "memory": 0.10443651117384434, "temperature": 37, "efficiency": 69.7}, {"timestamp": 1749480492261, "cpu": 0.0092, "memory": 0.10628332383930683, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749480494261, "cpu": 0.008, "memory": 0.1040551345795393, "temperature": 37, "efficiency": 69.8}, {"timestamp": 1749480496261, "cpu": 0.006, "memory": 0.10542050004005432, "temperature": 37, "efficiency": 69.5}, {"timestamp": 1749480498262, "cpu": 0.05109999999999999, "memory": 0.10683480650186539, "temperature": 37, "efficiency": 69.1}, {"timestamp": 1749480500264, "cpu": 0.0060999999999999995, "memory": 0.1053455751389265, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749480502265, "cpu": 0.0056, "memory": 0.10588369332253933, "temperature": 37, "efficiency": 70.5}, {"timestamp": 1749480504266, "cpu": 0.0060999999999999995, "memory": 0.10418100282549858, "temperature": 37, "efficiency": 69.8}, {"timestamp": 1749480506267, "cpu": 0.0287, "memory": 0.10647810995578766, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749480508267, "cpu": 0.025099999999999997, "memory": 0.10729655623435974, "temperature": 37, "efficiency": 69}, {"timestamp": 1749480510269, "cpu": 0.0060999999999999995, "memory": 0.1057951245456934, "temperature": 37, "efficiency": 69.4}, {"timestamp": 1749480512269, "cpu": 0.0055000000000000005, "memory": 0.10745530016720295, "temperature": 37, "efficiency": 69}, {"timestamp": 1749480514344, "cpu": 0.0082, "memory": 0.1081962138414383, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749480516345, "cpu": 0.0474, "memory": 0.10604201816022396, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749480518345, "cpu": 0.059000000000000004, "memory": 0.10750759392976761, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749480520347, "cpu": 0.0776, "memory": 0.10897237807512283, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749480522347, "cpu": 0.054299999999999994, "memory": 0.10681296698749065, "temperature": 37, "efficiency": 69.1}, {"timestamp": 1749480524350, "cpu": 0.038400000000000004, "memory": 0.10834881104528904, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749480526351, "cpu": 0.0688, "memory": 0.10977080091834068, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749480528351, "cpu": 0.0344, "memory": 0.10742121376097202, "temperature": 37, "efficiency": 69}, {"timestamp": 1749480530352, "cpu": 0.0369, "memory": 0.10979743674397469, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749480532353, "cpu": 0.039599999999999996, "memory": 0.10663275606930256, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749480534354, "cpu": 0.0436, "memory": 0.10811039246618748, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749480536356, "cpu": 0.0491, "memory": 0.11054142378270626, "temperature": 37, "efficiency": 68.2}, {"timestamp": 1749480538362, "cpu": 0.6342000000000001, "memory": 0.10698903352022171, "temperature": 37, "efficiency": 70}, {"timestamp": 1749480540358, "cpu": 0.0803, "memory": 0.10943603701889515, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749480542359, "cpu": 0.0796, "memory": 0.11106422170996666, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749480544362, "cpu": 0.067, "memory": 0.10766973719000816, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749480546362, "cpu": 0.0871, "memory": 0.11000954546034336, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749480548361, "cpu": 0.11069999999999999, "memory": 0.11140299029648304, "temperature": 37, "efficiency": 68}, {"timestamp": 1749480550364, "cpu": 0.05280000000000001, "memory": 0.10883272625505924, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749480552365, "cpu": 0.0916, "memory": 0.11022854596376419, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749480554367, "cpu": 0.0832, "memory": 0.11215298436582088, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749480556368, "cpu": 0.0628, "memory": 0.10968758724629879, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749480558368, "cpu": 0.6162, "memory": 0.11108373291790485, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749480560369, "cpu": 0.0535, "memory": 0.10976833291351795, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749480562371, "cpu": 0.0625, "memory": 0.11036773212254047, "temperature": 37, "efficiency": 68.2}, {"timestamp": 1749480564370, "cpu": 0.0521, "memory": 0.11190492659807205, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749480566374, "cpu": 0.0566, "memory": 0.11034626513719559, "temperature": 37, "efficiency": 68.2}, {"timestamp": 1749480568374, "cpu": 0.6566, "memory": 0.11080550029873848, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749480570377, "cpu": 0.0513, "memory": 0.11329976841807365, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749480572376, "cpu": 0.059199999999999996, "memory": 0.11084224097430706, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749480574378, "cpu": 0.0386, "memory": 0.1115236897021532, "temperature": 37, "efficiency": 68}, {"timestamp": 1749480576378, "cpu": 0.0545, "memory": 0.11379816569387913, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749480578378, "cpu": 0.7773, "memory": 0.11152788065373898, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749480580380, "cpu": 0.0774, "memory": 0.11303047649562359, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749480582380, "cpu": 0.1768, "memory": 0.11462662369012833, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749480584382, "cpu": 0.0767, "memory": 0.11165402829647064, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749480586383, "cpu": 0.08009999999999999, "memory": 0.11340072378516197, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749480588384, "cpu": 0.11689999999999999, "memory": 0.11498727835714817, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749480590386, "cpu": 0.0644, "memory": 0.11331778950989246, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749480592385, "cpu": 0.050199999999999995, "memory": 0.1139763742685318, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749480594386, "cpu": 0.0825, "memory": 0.11551803909242153, "temperature": 37, "efficiency": 67}, {"timestamp": 1749480596388, "cpu": 0.08009999999999999, "memory": 0.11377478949725628, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749480598388, "cpu": 0.2829, "memory": 0.11436617933213711, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749480600390, "cpu": 0.117, "memory": 0.11681411415338516, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749480602390, "cpu": 0.0485, "memory": 0.11449367739260197, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749480604391, "cpu": 0.08009999999999999, "memory": 0.11518201790750027, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749480606393, "cpu": 0.12719999999999998, "memory": 0.1174799632281065, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749480608393, "cpu": 0.1436, "memory": 0.11486220173537731, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749480610395, "cpu": 0.0872, "memory": 0.11629690416157246, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749480612396, "cpu": 0.0378, "memory": 0.11768098920583725, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749480614397, "cpu": 0.1188, "memory": 0.11534057557582855, "temperature": 37, "efficiency": 67}, {"timestamp": 1749480616398, "cpu": 0.038900000000000004, "memory": 0.11677071452140808, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749480618398, "cpu": 0.0777, "memory": 0.11827819980680943, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749480620399, "cpu": 0.0386, "memory": 0.11685537174344063, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749480622401, "cpu": 0.0352, "memory": 0.1173816155642271, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749480624402, "cpu": 0.0404, "memory": 0.11885170824825764, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749480626402, "cpu": 0.0363, "memory": 0.11711008846759796, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749480628409, "cpu": 0.6104, "memory": 0.11769789271056652, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749480630404, "cpu": 0.0522, "memory": 0.11610696092247963, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749480632405, "cpu": 0.0462, "memory": 0.11770548298954964, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749480634407, "cpu": 0.0584, "memory": 0.11824914254248142, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749480636407, "cpu": 0.0589, "memory": 0.12050545774400234, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749480638409, "cpu": 0.618, "memory": 0.1180584542453289, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749480640411, "cpu": 0.0426, "memory": 0.11948198080062866, "temperature": 37, "efficiency": 66}, {"timestamp": 1749480642411, "cpu": 0.045399999999999996, "memory": 0.12086252681910992, "temperature": 37, "efficiency": 65.7}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}