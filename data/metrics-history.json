{"timestamp": 1749480342967, "metrics": {"system": {"cpu": {"usage": 0.0105, "temperature": 37, "cores": 10}, "memory": {"used": 15818992, "total": 17179869184, "efficiency": 32.2445860745614}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749479442978}, "performance": {"uptime": 899.213, "efficiency": 72.7, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749480143819, "cpu": 0.0067, "memory": 0.11222371831536293, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749480145819, "cpu": 0.006600000000000001, "memory": 0.11054426431655884, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749480147820, "cpu": 0.0063999999999999994, "memory": 0.11099744588136673, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749480149821, "cpu": 0.0063, "memory": 0.10924716480076313, "temperature": 37, "efficiency": 66}, {"timestamp": 1749480151822, "cpu": 0.0105, "memory": 0.11067311279475689, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749480153823, "cpu": 0.008, "memory": 0.11144219897687435, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749480155824, "cpu": 0.0065, "memory": 0.10986081324517727, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749480157825, "cpu": 0.0063999999999999994, "memory": 0.11122091673314571, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749480159826, "cpu": 0.0118, "memory": 0.11263997294008732, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749480161830, "cpu": 0.0106, "memory": 0.11085434816777706, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749480163828, "cpu": 0.006600000000000001, "memory": 0.11239047162234783, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749480165829, "cpu": 0.0145, "memory": 0.11372179724276066, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749480167836, "cpu": 0.0095, "memory": 0.11147777549922466, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749480169836, "cpu": 0.0060999999999999995, "memory": 0.1138701569288969, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749480171837, "cpu": 0.0063999999999999994, "memory": 0.11459025554358959, "temperature": 37, "efficiency": 66}, {"timestamp": 1749480173837, "cpu": 0.0102, "memory": 0.11639194563031197, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749480175839, "cpu": 0.006200000000000001, "memory": 0.11874837800860405, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749480177840, "cpu": 0.018000000000000002, "memory": 0.11538341641426086, "temperature": 37, "efficiency": 65}, {"timestamp": 1749480179841, "cpu": 0.012, "memory": 0.11819307692348957, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749480181842, "cpu": 0.0106, "memory": 0.1156980637460947, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749480183843, "cpu": 0.007899999999999999, "memory": 0.11630984954535961, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749480185844, "cpu": 0.008, "memory": 0.11988063342869282, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749480187845, "cpu": 0.0106, "memory": 0.1173539087176323, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749480189846, "cpu": 0.0131, "memory": 0.1191379502415657, "temperature": 37, "efficiency": 64}, {"timestamp": 1749480191847, "cpu": 0.012, "memory": 0.11681132018566132, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749480193848, "cpu": 0.0123, "memory": 0.11831661686301231, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749480195850, "cpu": 0.0107, "memory": 0.11967816390097141, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749480197851, "cpu": 0.0119, "memory": 0.11735931038856506, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749480199853, "cpu": 0.0087, "memory": 0.11987960897386074, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749480201853, "cpu": 0.009899999999999999, "memory": 0.12048925273120403, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749480203855, "cpu": 0.0155, "memory": 0.1184435561299324, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749480205855, "cpu": 0.0109, "memory": 0.12075728736817837, "temperature": 37, "efficiency": 65}, {"timestamp": 1749480207856, "cpu": 0.006200000000000001, "memory": 0.12121926993131638, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749480209921, "cpu": 0.012400000000000001, "memory": 0.11981786228716373, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749480211922, "cpu": 0.0068, "memory": 0.12125479988753796, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749480213923, "cpu": 0.0068, "memory": 0.1218005083501339, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749480215924, "cpu": 0.0073, "memory": 0.12030885554850101, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749480217927, "cpu": 0.0073999999999999995, "memory": 0.12175585143268108, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749480219927, "cpu": 0.0072, "memory": 0.12326049618422985, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749480221928, "cpu": 0.006999999999999999, "memory": 0.12041530571877956, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749480223929, "cpu": 0.006600000000000001, "memory": 0.12200628407299519, "temperature": 37, "efficiency": 64}, {"timestamp": 1749480225930, "cpu": 0.01, "memory": 0.1234948169440031, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749480227932, "cpu": 0.0069, "memory": 0.12097805738449097, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749480229932, "cpu": 0.0088, "memory": 0.12351539917290211, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749480231934, "cpu": 0.009399999999999999, "memory": 0.12397877871990204, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749480233934, "cpu": 0.0068, "memory": 0.12130397371947765, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749480235936, "cpu": 0.0087, "memory": 0.12365938164293766, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749480237937, "cpu": 0.009899999999999999, "memory": 0.12425165623426437, "temperature": 37, "efficiency": 63.4}, {"timestamp": 1749480239937, "cpu": 0.0138, "memory": 0.12280181981623173, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749480241938, "cpu": 0.0073, "memory": 0.12426734901964664, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749480244149, "cpu": 0.0087, "memory": 0.12487848289310932, "temperature": 37, "efficiency": 64}, {"timestamp": 1749480246151, "cpu": 0.006600000000000001, "memory": 0.12325970456004143, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749480248152, "cpu": 0.006999999999999999, "memory": 0.12464341707527637, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749480250153, "cpu": 0.006200000000000001, "memory": 0.1260782591998577, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749480252153, "cpu": 0.0063, "memory": 0.12344480492174625, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749480254155, "cpu": 0.012, "memory": 0.12518195435404778, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749480256155, "cpu": 0.0071, "memory": 0.12659812346100807, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749480258156, "cpu": 0.0117, "memory": 0.12407032772898674, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749480260158, "cpu": 0.0101, "memory": 0.12650955468416214, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749480262157, "cpu": 0.0091, "memory": 0.1270366832613945, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749480264157, "cpu": 0.007600000000000001, "memory": 0.12430190108716488, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749480266156, "cpu": 0.0135, "memory": 0.12665675021708012, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749480268156, "cpu": 0.0098, "memory": 0.12711877934634686, "temperature": 37, "efficiency": 63.4}, {"timestamp": 1749480270156, "cpu": 0.0107, "memory": 0.12559620663523674, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749480272155, "cpu": 0.0102, "memory": 0.1269805245101452, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749480274156, "cpu": 0.0105, "memory": 0.12758984230458736, "temperature": 37, "efficiency": 64}, {"timestamp": 1749480276157, "cpu": 0.0114, "memory": 0.12599676847457886, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749480278157, "cpu": 0.01, "memory": 0.12761149555444717, "temperature": 37, "efficiency": 64}, {"timestamp": 1749480280157, "cpu": 0.009899999999999999, "memory": 0.12917597778141499, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749480282158, "cpu": 0.013200000000000002, "memory": 0.126689113676548, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749480284158, "cpu": 0.0107, "memory": 0.12831906788051128, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749480286158, "cpu": 0.01, "memory": 0.12967167422175407, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749480288160, "cpu": 0.007600000000000001, "memory": 0.1269773580133915, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749480290161, "cpu": 0.0085, "memory": 0.08349558338522911, "temperature": 37, "efficiency": 74.9}, {"timestamp": 1749480292162, "cpu": 0.0071, "memory": 0.0840525608509779, "temperature": 37, "efficiency": 74.7}, {"timestamp": 1749480294162, "cpu": 0.0063999999999999994, "memory": 0.08555781096220016, "temperature": 37, "efficiency": 74.3}, {"timestamp": 1749480296162, "cpu": 0.012, "memory": 0.08386657573282719, "temperature": 37, "efficiency": 74.8}, {"timestamp": 1749480298172, "cpu": 0.0105, "memory": 0.0842106994241476, "temperature": 37, "efficiency": 74.7}, {"timestamp": 1749480300170, "cpu": 0.0135, "memory": 0.08644950576126575, "temperature": 37, "efficiency": 74.1}, {"timestamp": 1749480302171, "cpu": 0.0097, "memory": 0.08427840657532215, "temperature": 37, "efficiency": 74.7}, {"timestamp": 1749480304173, "cpu": 0.0112, "memory": 0.0853631179779768, "temperature": 37, "efficiency": 74.4}, {"timestamp": 1749480306174, "cpu": 0.0123, "memory": 0.08788136765360832, "temperature": 37, "efficiency": 73.8}, {"timestamp": 1749480308174, "cpu": 0.0063, "memory": 0.08515482768416405, "temperature": 37, "efficiency": 74.4}, {"timestamp": 1749480310176, "cpu": 0.011000000000000001, "memory": 0.08704029023647308, "temperature": 37, "efficiency": 74}, {"timestamp": 1749480312177, "cpu": 0.0060999999999999995, "memory": 0.08831177838146687, "temperature": 37, "efficiency": 73.7}, {"timestamp": 1749480314177, "cpu": 0.0058, "memory": 0.08575017563998699, "temperature": 37, "efficiency": 74.3}, {"timestamp": 1749480316179, "cpu": 0.006, "memory": 0.08723679929971695, "temperature": 37, "efficiency": 73.9}, {"timestamp": 1749480318179, "cpu": 0.0068, "memory": 0.08907085284590721, "temperature": 37, "efficiency": 73.5}, {"timestamp": 1749480320180, "cpu": 0.006999999999999999, "memory": 0.0871540978550911, "temperature": 37, "efficiency": 74}, {"timestamp": 1749480322181, "cpu": 0.0083, "memory": 0.0876877922564745, "temperature": 37, "efficiency": 73.8}, {"timestamp": 1749480324182, "cpu": 0.006200000000000001, "memory": 0.08965618908405304, "temperature": 37, "efficiency": 73.3}, {"timestamp": 1749480326183, "cpu": 0.0088, "memory": 0.08799880743026733, "temperature": 37, "efficiency": 73.7}, {"timestamp": 1749480328184, "cpu": 0.0078, "memory": 0.08851904422044754, "temperature": 37, "efficiency": 73.6}, {"timestamp": 1749480330185, "cpu": 0.0063, "memory": 0.08712313137948513, "temperature": 37, "efficiency": 74.9}, {"timestamp": 1749480332186, "cpu": 0.006600000000000001, "memory": 0.08852239698171616, "temperature": 37, "efficiency": 74.6}, {"timestamp": 1749480334187, "cpu": 0.0077, "memory": 0.0889491755515337, "temperature": 37, "efficiency": 74.5}, {"timestamp": 1749480336189, "cpu": 0.012799999999999999, "memory": 0.09110239334404469, "temperature": 37, "efficiency": 73.9}, {"timestamp": 1749480338189, "cpu": 0.0078, "memory": 0.0882045365869999, "temperature": 37, "efficiency": 73.7}, {"timestamp": 1749480340190, "cpu": 0.007899999999999999, "memory": 0.0900766346603632, "temperature": 37, "efficiency": 73.2}, {"timestamp": 1749480342191, "cpu": 0.0105, "memory": 0.09207865223288536, "temperature": 37, "efficiency": 72.7}], "alerts": [], "optimizations": [{"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}