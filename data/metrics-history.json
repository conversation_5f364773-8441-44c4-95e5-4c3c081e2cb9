{"timestamp": 1749493420663, "metrics": {"system": {"cpu": {"usage": 0.03, "temperature": 37, "cores": 10}, "memory": {"used": 21912432, "total": 17179869184, "efficiency": 10.420046152880104}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749493120653}, "performance": {"uptime": 298.288, "efficiency": 65.5, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 99, "connections": 0}}, "history": [{"timestamp": 1749493220820, "cpu": 0.0082, "memory": 0.10143923573195934, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749493222822, "cpu": 0.043199999999999995, "memory": 0.10209456086158752, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749493224824, "cpu": 0.0245, "memory": 0.10269312188029289, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749493226825, "cpu": 0.0109, "memory": 0.10775458067655563, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749493228826, "cpu": 0.0162, "memory": 0.10833330452442169, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749493230827, "cpu": 0.018000000000000002, "memory": 0.11004675179719925, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749493232828, "cpu": 0.0202, "memory": 0.11065742000937462, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749493234829, "cpu": 0.0073, "memory": 0.11120224371552467, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749493236832, "cpu": 0.02, "memory": 0.10823635384440422, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749493238830, "cpu": 0.017, "memory": 0.10890751145780087, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749493240830, "cpu": 0.023, "memory": 0.11064470745623112, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749493242832, "cpu": 0.025799999999999997, "memory": 0.11136825196444988, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749493244833, "cpu": 0.0104, "memory": 0.10787849314510822, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749493246834, "cpu": 0.0283, "memory": 0.10955878533422947, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749493248835, "cpu": 0.0268, "memory": 0.11011455208063126, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749493250835, "cpu": 0.0091, "memory": 0.11184001341462135, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749493252837, "cpu": 0.0139, "memory": 0.1084260642528534, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749493254836, "cpu": 0.0154, "memory": 0.10900609195232391, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749493256840, "cpu": 0.0434, "memory": 0.11078203096985817, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749493258848, "cpu": 0.1413, "memory": 0.11128722690045834, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749493260850, "cpu": 0.3604, "memory": 0.11183759197592735, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749493262850, "cpu": 0.013200000000000002, "memory": 0.10933643206954002, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749493264852, "cpu": 0.2443, "memory": 0.10988614521920681, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749493266851, "cpu": 0.0107, "memory": 0.11182408779859543, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749493268852, "cpu": 0.0083, "memory": 0.11236118152737617, "temperature": 37, "efficiency": 64}, {"timestamp": 1749493270853, "cpu": 0.4098, "memory": 0.11294083669781685, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749493272855, "cpu": 0.0927, "memory": 0.11046081781387329, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749493274857, "cpu": 0.0571, "memory": 0.11097402311861515, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749493276858, "cpu": 0.0273, "memory": 0.11258730664849281, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749493278858, "cpu": 0.0105, "memory": 0.1131043303757906, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749493280860, "cpu": 0.3975, "memory": 0.10973908938467503, "temperature": 37, "efficiency": 65}, {"timestamp": 1749493282861, "cpu": 0.0599, "memory": 0.11152545921504498, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749493284863, "cpu": 0.0514, "memory": 0.11206674389541149, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749493286864, "cpu": 0.0155, "memory": 0.11734738945960999, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749493288864, "cpu": 0.0341, "memory": 0.11783977970480919, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749493290864, "cpu": 0.6723, "memory": 0.11845962144434452, "temperature": 37, "efficiency": 64}, {"timestamp": 1749493292866, "cpu": 0.0146, "memory": 0.12006680481135845, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749493294866, "cpu": 0.1908, "memory": 0.12061707675457001, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749493296868, "cpu": 0.025099999999999997, "memory": 0.11788071133196354, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749493298869, "cpu": 0.0156, "memory": 0.11860555969178677, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749493300870, "cpu": 0.37629999999999997, "memory": 0.11922023259103298, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749493302871, "cpu": 0.0093, "memory": 0.12092352844774723, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749493304871, "cpu": 0.0628, "memory": 0.11731088161468506, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749493306874, "cpu": 0.0687, "memory": 0.11916104704141617, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749493308875, "cpu": 0.0088, "memory": 0.11967914178967476, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749493310875, "cpu": 0.5598, "memory": 0.12033907696604729, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749493312882, "cpu": 0.1514, "memory": 0.11791344732046127, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749493314878, "cpu": 0.21549999999999997, "memory": 0.11863806284964085, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749493316878, "cpu": 0.0134, "memory": 0.12024459429085255, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749493318879, "cpu": 0.0303, "memory": 0.12565450742840767, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749493320883, "cpu": 0.9173, "memory": 0.132442032918334, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749493322884, "cpu": 0.0365, "memory": 0.1264669932425022, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749493324884, "cpu": 0.053799999999999994, "memory": 0.1260190736502409, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749493326885, "cpu": 0.0465, "memory": 0.12778420932590961, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749493328888, "cpu": 0.027700000000000002, "memory": 0.12769363820552826, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749493330888, "cpu": 0.5622, "memory": 0.12799291871488094, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749493332889, "cpu": 0.0442, "memory": 0.12967507354915142, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749493334896, "cpu": 0.08009999999999999, "memory": 0.14122948050498962, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749493336896, "cpu": 0.0634, "memory": 0.13184347189962864, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749493338898, "cpu": 0.013999999999999999, "memory": 0.13235914520919323, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749493340898, "cpu": 0.21280000000000002, "memory": 0.13200961984694004, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749493342899, "cpu": 0.0393, "memory": 0.09406623430550098, "temperature": 37, "efficiency": 69.1}, {"timestamp": 1749493344899, "cpu": 0.08639999999999999, "memory": 0.09484495967626572, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749493346901, "cpu": 0.052700000000000004, "memory": 0.09691044688224792, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749493348901, "cpu": 0.0321, "memory": 0.09962990880012512, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749493350904, "cpu": 0.6456, "memory": 0.10628821328282356, "temperature": 37, "efficiency": 68.2}, {"timestamp": 1749493352909, "cpu": 0.05910000000000001, "memory": 0.0970061868429184, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749493354903, "cpu": 0.0926, "memory": 0.09681177325546741, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749493356904, "cpu": 0.0429, "memory": 0.09887916967272758, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749493358904, "cpu": 0.015200000000000002, "memory": 0.10442901402711868, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749493360906, "cpu": 0.246, "memory": 0.11392878368496895, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749493362907, "cpu": 0.0223, "memory": 0.10775006376206875, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749493364908, "cpu": 0.0369, "memory": 0.10712561197578907, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749493366909, "cpu": 0.034999999999999996, "memory": 0.10815979912877083, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749493368910, "cpu": 0.0226, "memory": 0.10878415778279305, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749493370910, "cpu": 0.6657, "memory": 0.10844417847692966, "temperature": 37, "efficiency": 67}, {"timestamp": 1749493372910, "cpu": 0.097, "memory": 0.1121514942497015, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749493374911, "cpu": 0.0364, "memory": 0.11847415007650852, "temperature": 37, "efficiency": 66}, {"timestamp": 1749493376913, "cpu": 0.0166, "memory": 0.11566830798983574, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749493378912, "cpu": 0.0629, "memory": 0.11570318602025509, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749493380914, "cpu": 0.764, "memory": 0.1160849817097187, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749493382916, "cpu": 0.021, "memory": 0.11984268203377724, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749493384917, "cpu": 0.1171, "memory": 0.12627467513084412, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749493386917, "cpu": 0.0207, "memory": 0.11735567823052406, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749493388919, "cpu": 0.0568, "memory": 0.11695995926856995, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749493390920, "cpu": 0.3139, "memory": 0.11714966967701912, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749493392921, "cpu": 0.019799999999999998, "memory": 0.11871703900396824, "temperature": 37, "efficiency": 66}, {"timestamp": 1749493394922, "cpu": 0.0449, "memory": 0.11768336407840252, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749493396922, "cpu": 0.0372, "memory": 0.11831163428723812, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749493398924, "cpu": 0.0184, "memory": 0.11883866973221302, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749493400925, "cpu": 0.45189999999999997, "memory": 0.11872597970068455, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749493402926, "cpu": 0.031100000000000003, "memory": 0.12235916219651699, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749493404926, "cpu": 0.1088, "memory": 0.12877103872597218, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749493406947, "cpu": 0.0915, "memory": 0.12631532736122608, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749493408931, "cpu": 0.059199999999999996, "memory": 0.12588291428983212, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749493410933, "cpu": 0.44799999999999995, "memory": 0.1260626595467329, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749493412939, "cpu": 0.05159999999999999, "memory": 0.12674201279878616, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749493414963, "cpu": 0.1887, "memory": 0.12638457119464874, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749493416939, "cpu": 0.0415, "memory": 0.1280114520341158, "temperature": 37, "efficiency": 65}, {"timestamp": 1749493418941, "cpu": 0.03, "memory": 0.12754714116454124, "temperature": 37, "efficiency": 65.5}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}