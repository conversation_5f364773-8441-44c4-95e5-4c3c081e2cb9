{"timestamp": 1749476265587, "metrics": {"system": {"cpu": {"usage": 0.0388, "temperature": 37, "cores": 10}, "memory": {"used": 13588936, "total": 17179869184, "efficiency": 39.94186589201666}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749475965584}, "performance": {"uptime": 298.425, "efficiency": 75.3, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 99, "connections": 0}}, "history": [{"timestamp": 1749476065836, "cpu": 0.009600000000000001, "memory": 0.08399281650781631, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749476067840, "cpu": 0.012199999999999999, "memory": 0.08612619712948799, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749476069842, "cpu": 0.0182, "memory": 0.08500944823026657, "temperature": 37, "efficiency": 65}, {"timestamp": 1749476071843, "cpu": 0.014899999999999998, "memory": 0.08582426235079765, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749476073845, "cpu": 0.0103, "memory": 0.08895318023860455, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749476075847, "cpu": 0.029, "memory": 0.08803866803646088, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749476077850, "cpu": 0.010799999999999999, "memory": 0.08620209991931915, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749476079850, "cpu": 0.0123, "memory": 0.0883852131664753, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749476081851, "cpu": 0.009399999999999999, "memory": 0.08692527189850807, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749476083853, "cpu": 0.0105, "memory": 0.0882523600012064, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749476085856, "cpu": 0.0162, "memory": 0.08761417120695114, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749476087858, "cpu": 0.038400000000000004, "memory": 0.0897394958883524, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749476089857, "cpu": 0.0139, "memory": 0.08862814866006374, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749476091862, "cpu": 0.0235, "memory": 0.09011523798108101, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749476093863, "cpu": 0.0115, "memory": 0.08921683765947819, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749476095865, "cpu": 0.0098, "memory": 0.09147822856903076, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749476097867, "cpu": 0.0111, "memory": 0.0892487820237875, "temperature": 37, "efficiency": 65}, {"timestamp": 1749476099868, "cpu": 0.0135, "memory": 0.09218365885317326, "temperature": 37, "efficiency": 64}, {"timestamp": 1749476101871, "cpu": 0.0114, "memory": 0.08980552665889263, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749476103870, "cpu": 0.0115, "memory": 0.0919888261705637, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749476105874, "cpu": 0.0426, "memory": 0.09062960743904114, "temperature": 37, "efficiency": 65}, {"timestamp": 1749476107876, "cpu": 0.3643, "memory": 0.09113801643252373, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749476109877, "cpu": 0.0193, "memory": 0.09099384769797325, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749476111879, "cpu": 0.0134, "memory": 0.09328126907348633, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749476113881, "cpu": 0.21189999999999998, "memory": 0.09461869485676289, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749476115883, "cpu": 0.0178, "memory": 0.0938687939196825, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749476117884, "cpu": 0.12190000000000001, "memory": 0.0920694787055254, "temperature": 37, "efficiency": 65}, {"timestamp": 1749476119885, "cpu": 0.02, "memory": 0.09502973407506943, "temperature": 37, "efficiency": 64}, {"timestamp": 1749476121888, "cpu": 0.010799999999999999, "memory": 0.09647971019148827, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749476123889, "cpu": 0.012199999999999999, "memory": 0.09539523161947727, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749476125891, "cpu": 0.0084, "memory": 0.09680981747806072, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749476127893, "cpu": 0.0135, "memory": 0.09556729346513748, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749476129896, "cpu": 0.0116, "memory": 0.09447126649320126, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749476131897, "cpu": 0.0197, "memory": 0.0958658754825592, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749476133900, "cpu": 0.0102, "memory": 0.09798589162528515, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749476135899, "cpu": 0.0112, "memory": 0.09688152931630611, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749476137902, "cpu": 0.3584, "memory": 0.09734281338751316, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749476139902, "cpu": 0.0125, "memory": 0.0974155031144619, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749476141904, "cpu": 0.0313, "memory": 0.09957081638276577, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749476143906, "cpu": 0.025599999999999998, "memory": 0.09767417795956135, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749476145908, "cpu": 0.0228, "memory": 0.0998922623693943, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749476147910, "cpu": 0.1748, "memory": 0.0976713839918375, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749476149911, "cpu": 0.0305, "memory": 0.09986530058085918, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749476151913, "cpu": 0.026, "memory": 0.0985605176538229, "temperature": 37, "efficiency": 66}, {"timestamp": 1749476153914, "cpu": 0.031799999999999995, "memory": 0.10136147029697895, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749476155916, "cpu": 0.034999999999999996, "memory": 0.0996625516563654, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749476157918, "cpu": 0.0287, "memory": 0.10174307972192764, "temperature": 37, "efficiency": 63.3}, {"timestamp": 1749476159918, "cpu": 0.055, "memory": 0.1002623699605465, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749476161920, "cpu": 0.034699999999999995, "memory": 0.10166456922888756, "temperature": 37, "efficiency": 63.3}, {"timestamp": 1749476163924, "cpu": 0.0346, "memory": 0.09933724068105221, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749476165926, "cpu": 0.1015, "memory": 0.10223719291388988, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749476167927, "cpu": 0.0373, "memory": 0.10068323463201523, "temperature": 37, "efficiency": 64}, {"timestamp": 1749476169930, "cpu": 0.034499999999999996, "memory": 0.10275859385728836, "temperature": 37, "efficiency": 63.4}, {"timestamp": 1749476171929, "cpu": 0.0643, "memory": 0.10106004774570465, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749476173931, "cpu": 0.0392, "memory": 0.10364637710154057, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749476175933, "cpu": 0.0312, "memory": 0.10211961343884468, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749476177934, "cpu": 0.0321, "memory": 0.10499348863959312, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749476179935, "cpu": 0.0353, "memory": 0.10226843878626823, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749476181937, "cpu": 0.0495, "memory": 0.10437178425490856, "temperature": 37, "efficiency": 65}, {"timestamp": 1749476183939, "cpu": 0.029599999999999998, "memory": 0.10333852842450142, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749476185941, "cpu": 0.0281, "memory": 0.10555698536336422, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749476187943, "cpu": 0.0364, "memory": 0.10356712155044079, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749476189944, "cpu": 0.0321, "memory": 0.10649333707988262, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749476191946, "cpu": 0.0343, "memory": 0.10441271588206291, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749476193948, "cpu": 0.0576, "memory": 0.10656584054231644, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749476195951, "cpu": 0.0301, "memory": 0.10530739091336727, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749476197953, "cpu": 0.0315, "memory": 0.10733557865023613, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749476199953, "cpu": 0.026, "memory": 0.10941014625132084, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749476201954, "cpu": 0.025099999999999997, "memory": 0.10784384794533253, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749476203957, "cpu": 0.0273, "memory": 0.10994002223014832, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749476205960, "cpu": 0.0545, "memory": 0.10802643373608589, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749476207959, "cpu": 0.032, "memory": 0.11152736842632294, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749476209961, "cpu": 0.0362, "memory": 0.11012386530637741, "temperature": 37, "efficiency": 65}, {"timestamp": 1749476211964, "cpu": 0.0302, "memory": 0.1114195678383112, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749476213966, "cpu": 0.029799999999999997, "memory": 0.11043976992368698, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749476215968, "cpu": 0.033, "memory": 0.1125615555793047, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749476217969, "cpu": 0.0408, "memory": 0.11060256510972977, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749476219987, "cpu": 0.1245, "memory": 0.11328011751174927, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749476221973, "cpu": 0.0456, "memory": 0.1105661503970623, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749476223974, "cpu": 0.030600000000000002, "memory": 0.11261394247412682, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749476225976, "cpu": 0.0342, "memory": 0.11157337576150894, "temperature": 37, "efficiency": 65}, {"timestamp": 1749476227977, "cpu": 0.0336, "memory": 0.11356924660503864, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749476229977, "cpu": 0.025599999999999998, "memory": 0.11198087595403194, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749476231979, "cpu": 0.0238, "memory": 0.11416403576731682, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749476233981, "cpu": 0.0279, "memory": 0.11220988817512989, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749476235984, "cpu": 0.0218, "memory": 0.11436231434345245, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749476237986, "cpu": 0.0377, "memory": 0.11335350573062897, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749476239988, "cpu": 0.0224, "memory": 0.11529461480677128, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749476241989, "cpu": 0.0349, "memory": 0.11660298332571983, "temperature": 37, "efficiency": 64}, {"timestamp": 1749476243991, "cpu": 0.041100000000000005, "memory": 0.11548115871846676, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749476245993, "cpu": 0.027, "memory": 0.11765975505113602, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749476247994, "cpu": 0.0364, "memory": 0.1156975980848074, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749476249996, "cpu": 0.0313, "memory": 0.07521631196141243, "temperature": 37, "efficiency": 75.4}, {"timestamp": 1749476251998, "cpu": 0.045399999999999996, "memory": 0.07670251652598381, "temperature": 37, "efficiency": 75}, {"timestamp": 1749476253999, "cpu": 0.0339, "memory": 0.07926439866423607, "temperature": 37, "efficiency": 74.3}, {"timestamp": 1749476256001, "cpu": 0.0348, "memory": 0.07765209302306175, "temperature": 37, "efficiency": 74.7}, {"timestamp": 1749476258004, "cpu": 0.0286, "memory": 0.07984763942658901, "temperature": 37, "efficiency": 74.1}, {"timestamp": 1749476260005, "cpu": 0.0468, "memory": 0.07824297063052654, "temperature": 37, "efficiency": 74.6}, {"timestamp": 1749476262007, "cpu": 0.0322, "memory": 0.0770780723541975, "temperature": 37, "efficiency": 75.8}, {"timestamp": 1749476264009, "cpu": 0.0388, "memory": 0.07909801788628101, "temperature": 37, "efficiency": 75.3}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}