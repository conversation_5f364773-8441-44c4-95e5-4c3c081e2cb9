{"timestamp": 1749477466383, "metrics": {"system": {"cpu": {"usage": 0.0221, "temperature": 37, "cores": 10}, "memory": {"used": 13828840, "total": 17179869184, "efficiency": 32.63803590133679}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749477166378}, "performance": {"uptime": 298.431, "efficiency": 72.9, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 99, "connections": 0}}, "history": [{"timestamp": 1749477266539, "cpu": 0.061399999999999996, "memory": 0.08467654697597027, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749477268540, "cpu": 0.06530000000000001, "memory": 0.0861522275954485, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749477270541, "cpu": 0.0923, "memory": 0.08532935753464699, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749477272541, "cpu": 0.1978, "memory": 0.08796756155788898, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749477274542, "cpu": 0.0677, "memory": 0.08615832775831223, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749477276545, "cpu": 0.0339, "memory": 0.08844374679028988, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749477278549, "cpu": 0.0536, "memory": 0.08775237947702408, "temperature": 37, "efficiency": 65}, {"timestamp": 1749477280547, "cpu": 0.0287, "memory": 0.08916626684367657, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749477282549, "cpu": 0.8435, "memory": 0.08717980235815048, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749477284549, "cpu": 0.0557, "memory": 0.09040250442922115, "temperature": 37, "efficiency": 66}, {"timestamp": 1749477286591, "cpu": 0.9716, "memory": 0.09089051745831966, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749477288589, "cpu": 0.0386, "memory": 0.08987998589873314, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749477290592, "cpu": 0.0464, "memory": 0.08788062259554863, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749477292607, "cpu": 0.9029, "memory": 0.08944631554186344, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749477294607, "cpu": 0.0181, "memory": 0.08978089317679405, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749477296610, "cpu": 0.016, "memory": 0.09257015772163868, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749477298607, "cpu": 0.4681, "memory": 0.09002629667520523, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749477300671, "cpu": 0.10289999999999999, "memory": 0.09363684803247452, "temperature": 37, "efficiency": 64}, {"timestamp": 1749477302647, "cpu": 0.6419, "memory": 0.09270859882235527, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749477304644, "cpu": 0.0119, "memory": 0.09112139232456684, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749477306645, "cpu": 0.1912, "memory": 0.09341924451291561, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749477308784, "cpu": 0.0947, "memory": 0.09278645738959312, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749477310679, "cpu": 0.0928, "memory": 0.09430386126041412, "temperature": 37, "efficiency": 66}, {"timestamp": 1749477312681, "cpu": 0.8511, "memory": 0.09242603555321693, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749477314677, "cpu": 0.0611, "memory": 0.09259255602955818, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749477316677, "cpu": 0.8354999999999999, "memory": 0.09316764771938324, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749477318678, "cpu": 0.0334, "memory": 0.09662783704698086, "temperature": 37, "efficiency": 64}, {"timestamp": 1749477320681, "cpu": 0.0297, "memory": 0.09552836418151855, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749477322679, "cpu": 0.0257, "memory": 0.09391349740326405, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749477324679, "cpu": 0.0222, "memory": 0.09627924300730228, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749477326682, "cpu": 0.0628, "memory": 0.0944369938224554, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749477328683, "cpu": 0.046, "memory": 0.0959406141191721, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749477330686, "cpu": 0.0967, "memory": 0.095311738550663, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749477332688, "cpu": 0.08750000000000001, "memory": 0.09784610010683537, "temperature": 37, "efficiency": 64}, {"timestamp": 1749477334686, "cpu": 0.0332, "memory": 0.09701987728476524, "temperature": 37, "efficiency": 66}, {"timestamp": 1749477336687, "cpu": 0.1275, "memory": 0.09844875894486904, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749477338689, "cpu": 0.0241, "memory": 0.09788358584046364, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749477340722, "cpu": 0.051500000000000004, "memory": 0.1001597847789526, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749477342693, "cpu": 0.1958, "memory": 0.09795352816581726, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749477344699, "cpu": 0.0678, "memory": 0.09784973226487637, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749477346697, "cpu": 0.2715, "memory": 0.09835613891482353, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749477348699, "cpu": 0.0883, "memory": 0.09768609888851643, "temperature": 37, "efficiency": 65}, {"timestamp": 1749477350700, "cpu": 0.0252, "memory": 0.10088551789522171, "temperature": 37, "efficiency": 64}, {"timestamp": 1749477352700, "cpu": 0.031100000000000003, "memory": 0.1014727633446455, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749477354719, "cpu": 0.0683, "memory": 0.10108510032296181, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749477356707, "cpu": 0.3237, "memory": 0.09874631650745869, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749477358705, "cpu": 0.05159999999999999, "memory": 0.10210280306637287, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749477360706, "cpu": 0.0567, "memory": 0.10026916861534119, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749477362707, "cpu": 0.0373, "memory": 0.10263477452099323, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749477364708, "cpu": 0.0161, "memory": 0.10133981704711914, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749477366710, "cpu": 0.1008, "memory": 0.10280604474246502, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749477368711, "cpu": 0.0131, "memory": 0.1017239410430193, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749477370712, "cpu": 0.0164, "memory": 0.10400624014437199, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749477372713, "cpu": 0.0115, "memory": 0.10268464684486389, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749477374714, "cpu": 0.012799999999999999, "memory": 0.10568704456090927, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749477376715, "cpu": 0.1035, "memory": 0.10270192287862301, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749477378723, "cpu": 0.0626, "memory": 0.10577053762972355, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749477380723, "cpu": 0.0481, "memory": 0.10532783344388008, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749477382728, "cpu": 0.0687, "memory": 0.10353121906518936, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749477384735, "cpu": 0.0625, "memory": 0.10574427433311939, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749477386739, "cpu": 0.3469, "memory": 0.10415501892566681, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749477388734, "cpu": 0.07529999999999999, "memory": 0.10554171167314053, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749477390732, "cpu": 0.0194, "memory": 0.10693557560443878, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749477392734, "cpu": 0.0168, "memory": 0.10471837595105171, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749477394734, "cpu": 0.0117, "memory": 0.10613007470965385, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749477396734, "cpu": 0.0199, "memory": 0.10660393163561821, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749477398736, "cpu": 0.0301, "memory": 0.1087697222828865, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749477400755, "cpu": 0.025, "memory": 0.10605999268591404, "temperature": 37, "efficiency": 65}, {"timestamp": 1749477402756, "cpu": 0.012400000000000001, "memory": 0.10741716250777245, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749477404758, "cpu": 0.0226, "memory": 0.10870462283492088, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749477406760, "cpu": 0.0629, "memory": 0.10593431070446968, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749477408759, "cpu": 0.025, "memory": 0.10730195790529251, "temperature": 37, "efficiency": 65}, {"timestamp": 1749477410761, "cpu": 0.0077, "memory": 0.1087515614926815, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749477412761, "cpu": 0.0069, "memory": 0.10680416598916054, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749477414761, "cpu": 0.012, "memory": 0.10738028213381767, "temperature": 37, "efficiency": 65}, {"timestamp": 1749477416762, "cpu": 0.6446999999999999, "memory": 0.108668627217412, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749477418763, "cpu": 0.0121, "memory": 0.11082934215664864, "temperature": 37, "efficiency": 64}, {"timestamp": 1749477420764, "cpu": 0.012899999999999998, "memory": 0.10748235508799553, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749477422765, "cpu": 0.0126, "memory": 0.10976619087159634, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749477424766, "cpu": 0.0182, "memory": 0.11107479222118855, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749477426767, "cpu": 0.0604, "memory": 0.10761921294033527, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749477428767, "cpu": 0.0081, "memory": 0.0756874680519104, "temperature": 37, "efficiency": 75.2}, {"timestamp": 1749477430768, "cpu": 0.01, "memory": 0.07704831659793854, "temperature": 37, "efficiency": 74.9}, {"timestamp": 1749477432769, "cpu": 0.0081, "memory": 0.07854066789150238, "temperature": 37, "efficiency": 74.5}, {"timestamp": 1749477434771, "cpu": 0.029500000000000002, "memory": 0.07580653764307499, "temperature": 37, "efficiency": 74.2}, {"timestamp": 1749477436793, "cpu": 1.2959999999999998, "memory": 0.07653222419321537, "temperature": 37, "efficiency": 73.6}, {"timestamp": 1749477438796, "cpu": 0.1191, "memory": 0.07859240286052227, "temperature": 37, "efficiency": 73.4}, {"timestamp": 1749477440795, "cpu": 0.0195, "memory": 0.0799945555627346, "temperature": 37, "efficiency": 73}, {"timestamp": 1749477442796, "cpu": 0.0127, "memory": 0.07780012674629688, "temperature": 37, "efficiency": 73.6}, {"timestamp": 1749477444798, "cpu": 0.0103, "memory": 0.07870448753237724, "temperature": 37, "efficiency": 73.4}, {"timestamp": 1749477446798, "cpu": 0.077, "memory": 0.08011828176677227, "temperature": 37, "efficiency": 73}, {"timestamp": 1749477448799, "cpu": 0.015899999999999997, "memory": 0.07806415669620037, "temperature": 37, "efficiency": 73.6}, {"timestamp": 1749477450801, "cpu": 0.0157, "memory": 0.07873829454183578, "temperature": 37, "efficiency": 73.4}, {"timestamp": 1749477452802, "cpu": 0.0252, "memory": 0.08118129335343838, "temperature": 37, "efficiency": 72.7}, {"timestamp": 1749477454804, "cpu": 0.0194, "memory": 0.07818685844540596, "temperature": 37, "efficiency": 73.5}, {"timestamp": 1749477456806, "cpu": 0.0854, "memory": 0.07873452268540859, "temperature": 37, "efficiency": 73.3}, {"timestamp": 1749477458806, "cpu": 0.0154, "memory": 0.08073803037405014, "temperature": 37, "efficiency": 72.8}, {"timestamp": 1749477460808, "cpu": 0.016, "memory": 0.07813083939254284, "temperature": 37, "efficiency": 73.5}, {"timestamp": 1749477462808, "cpu": 0.0118, "memory": 0.07935645990073681, "temperature": 37, "efficiency": 73.2}, {"timestamp": 1749477464809, "cpu": 0.0221, "memory": 0.08049444295465946, "temperature": 37, "efficiency": 72.9}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}