{"timestamp": 1749484491514, "metrics": {"system": {"cpu": {"usage": 0.0401, "temperature": 37, "cores": 10}, "memory": {"used": 17014968, "total": 17179869184, "efficiency": 31.178844899353876}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749484191486}, "performance": {"uptime": 298.541, "efficiency": 72.4, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 99, "connections": 0}}, "history": [{"timestamp": 1749484291675, "cpu": 0.0086, "memory": 0.10745162144303322, "temperature": 37, "efficiency": 65}, {"timestamp": 1749484293676, "cpu": 0.018000000000000002, "memory": 0.10815039277076721, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749484295677, "cpu": 0.0178, "memory": 0.10683834552764893, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749484297679, "cpu": 0.016, "memory": 0.10947328992187977, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749484299680, "cpu": 0.0171, "memory": 0.10609901510179043, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749484301681, "cpu": 0.0073, "memory": 0.10974076576530933, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749484303682, "cpu": 0.0095, "memory": 0.10771369561553001, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749484305683, "cpu": 0.0093, "memory": 0.10924497619271278, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749484307684, "cpu": 0.0075, "memory": 0.1081476453691721, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749484309685, "cpu": 0.0084, "memory": 0.1099076122045517, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749484311685, "cpu": 0.0263, "memory": 0.10839719325304031, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749484313686, "cpu": 0.006999999999999999, "memory": 0.11012684553861618, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749484315687, "cpu": 0.0075, "memory": 0.10849894024431705, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749484317688, "cpu": 0.052, "memory": 0.11026314459741116, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749484319689, "cpu": 0.0288, "memory": 0.1118894200772047, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749484321691, "cpu": 0.012, "memory": 0.11123120784759521, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749484323691, "cpu": 0.0147, "memory": 0.11194683611392975, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749484325693, "cpu": 0.0144, "memory": 0.11059632524847984, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749484327695, "cpu": 0.0241, "memory": 0.11357278563082218, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749484329696, "cpu": 0.0315, "memory": 0.11034486815333366, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749484331696, "cpu": 0.0073999999999999995, "memory": 0.11403379030525684, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749484333698, "cpu": 0.011000000000000001, "memory": 0.11178595013916492, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749484335698, "cpu": 0.0073, "memory": 0.11346298269927502, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749484337700, "cpu": 0.025300000000000003, "memory": 0.11220015585422516, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749484339929, "cpu": 0.3484, "memory": 0.11350014247000217, "temperature": 37, "efficiency": 64}, {"timestamp": 1749484341880, "cpu": 0.0354, "memory": 0.11260523460805416, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749484343880, "cpu": 0.041, "memory": 0.1143318135291338, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749484345881, "cpu": 0.014899999999999998, "memory": 0.11538425460457802, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749484347882, "cpu": 0.0103, "memory": 0.11312835849821568, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749484349881, "cpu": 0.0335, "memory": 0.11492129415273666, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749484351894, "cpu": 0.0287, "memory": 0.11481130495667458, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749484353893, "cpu": 0.014899999999999998, "memory": 0.11552334763109684, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749484355895, "cpu": 0.0084, "memory": 0.11380515061318874, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749484357895, "cpu": 0.0073999999999999995, "memory": 0.11654510162770748, "temperature": 37, "efficiency": 64}, {"timestamp": 1749484359896, "cpu": 0.0342, "memory": 0.11717844754457474, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749484361955, "cpu": 0.0451, "memory": 0.1168315764516592, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749484363982, "cpu": 0.0883, "memory": 0.11848411522805691, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749484365955, "cpu": 0.0119, "memory": 0.11626151390373707, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749484367957, "cpu": 0.0154, "memory": 0.11896258220076561, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749484369957, "cpu": 0.01, "memory": 0.11660424061119556, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749484371958, "cpu": 0.0178, "memory": 0.11938582174479961, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749484373959, "cpu": 0.0092, "memory": 0.11657709255814552, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749484375960, "cpu": 0.0075, "memory": 0.1191385556012392, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749484377961, "cpu": 0.0145, "memory": 0.11729816906154156, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749484379961, "cpu": 0.02, "memory": 0.11898083612322807, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749484381962, "cpu": 0.025799999999999997, "memory": 0.11813938617706299, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749484383964, "cpu": 0.0137, "memory": 0.11869603767991066, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749484386115, "cpu": 0.2865, "memory": 0.1215111929923296, "temperature": 37, "efficiency": 63.4}, {"timestamp": 1749484387970, "cpu": 0.019100000000000002, "memory": 0.12044645845890045, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749484389972, "cpu": 0.0179, "memory": 0.12100227177143097, "temperature": 37, "efficiency": 64}, {"timestamp": 1749484391971, "cpu": 0.034699999999999995, "memory": 0.12054732069373131, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749484393973, "cpu": 0.0186, "memory": 0.12242305092513561, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749484395973, "cpu": 0.0168, "memory": 0.11990582570433617, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749484397975, "cpu": 0.018799999999999997, "memory": 0.12247040867805481, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749484399976, "cpu": 0.0166, "memory": 0.11987732723355293, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749484401991, "cpu": 0.0599, "memory": 0.1228113193064928, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749484403980, "cpu": 0.0385, "memory": 0.12044203467667103, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749484405979, "cpu": 0.0168, "memory": 0.12105964124202728, "temperature": 37, "efficiency": 65}, {"timestamp": 1749484407980, "cpu": 0.0361, "memory": 0.12307562865316868, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749484409988, "cpu": 0.0741, "memory": 0.12462921440601349, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749484411990, "cpu": 0.1273, "memory": 0.12429379858076572, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749484413985, "cpu": 0.0477, "memory": 0.12500034645199776, "temperature": 37, "efficiency": 64}, {"timestamp": 1749484415986, "cpu": 0.014100000000000001, "memory": 0.12363339774310589, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749484417989, "cpu": 0.0594, "memory": 0.12621735222637653, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749484419989, "cpu": 0.0223, "memory": 0.12279152870178223, "temperature": 37, "efficiency": 65}, {"timestamp": 1749484421990, "cpu": 0.6858000000000001, "memory": 0.1262984238564968, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749484423992, "cpu": 0.026699999999999998, "memory": 0.12395312078297138, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749484425993, "cpu": 0.0189, "memory": 0.12543611228466034, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749484428000, "cpu": 0.1608, "memory": 0.12425766326487064, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749484429995, "cpu": 0.0238, "memory": 0.12588342651724815, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749484431996, "cpu": 1.2785, "memory": 0.12448625639081001, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749484433997, "cpu": 0.0155, "memory": 0.12596738524734974, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749484435998, "cpu": 0.0518, "memory": 0.12844600714743137, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749484437999, "cpu": 0.0314, "memory": 0.12595145963132381, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749484440000, "cpu": 0.0325, "memory": 0.12768330052495003, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749484442001, "cpu": 0.6462, "memory": 0.126961013302207, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749484444002, "cpu": 0.026899999999999997, "memory": 0.127665838226676, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749484446003, "cpu": 0.0259, "memory": 0.12634783051908016, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749484448003, "cpu": 0.015, "memory": 0.1289146952331066, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749484450005, "cpu": 0.008, "memory": 0.12951791286468506, "temperature": 37, "efficiency": 64}, {"timestamp": 1749484452006, "cpu": 0.5971, "memory": 0.12918631546199322, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749484454006, "cpu": 0.025, "memory": 0.13083750382065773, "temperature": 37, "efficiency": 64}, {"timestamp": 1749484456008, "cpu": 0.0165, "memory": 0.12842006981372833, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749484458009, "cpu": 0.025599999999999998, "memory": 0.13078851625323296, "temperature": 37, "efficiency": 64}, {"timestamp": 1749484460010, "cpu": 0.0157, "memory": 0.12838514521718025, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749484462010, "cpu": 0.6931999999999999, "memory": 0.13092630542814732, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749484464011, "cpu": 0.0213, "memory": 0.12847254984080791, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749484466012, "cpu": 0.0408, "memory": 0.13255318626761436, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749484468013, "cpu": 0.0275, "memory": 0.13045216910541058, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749484470014, "cpu": 0.012, "memory": 0.1318264752626419, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749484472016, "cpu": 0.6795, "memory": 0.1310762483626604, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749484474023, "cpu": 0.051500000000000004, "memory": 0.13161608949303627, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749484476037, "cpu": 0.0701, "memory": 0.133938854560256, "temperature": 37, "efficiency": 64}, {"timestamp": 1749484478021, "cpu": 0.0337, "memory": 0.13240608386695385, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749484480022, "cpu": 0.0185, "memory": 0.13291179202497005, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749484482023, "cpu": 0.5937, "memory": 0.13229032047092915, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749484484024, "cpu": 0.0254, "memory": 0.13386327773332596, "temperature": 37, "efficiency": 64}, {"timestamp": 1749484486025, "cpu": 0.0241, "memory": 0.13129571452736855, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749484488025, "cpu": 0.013, "memory": 0.09751156903803349, "temperature": 37, "efficiency": 72.7}, {"timestamp": 1749484490027, "cpu": 0.0401, "memory": 0.09904014877974987, "temperature": 37, "efficiency": 72.4}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}