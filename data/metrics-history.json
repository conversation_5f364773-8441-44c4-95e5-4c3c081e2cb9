{"timestamp": 1749486160176, "metrics": {"system": {"cpu": {"usage": 0.040299999999999996, "temperature": 37, "cores": 10}, "memory": {"used": 21730280, "total": 17179869184, "efficiency": 10.202367287999323}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749485860173}, "performance": {"uptime": 298.254, "efficiency": 65.4, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 99, "connections": 0}}, "history": [{"timestamp": 1749485960327, "cpu": 0.0095, "memory": 0.09535104036331177, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749485962329, "cpu": 0.013, "memory": 0.10740002617239952, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749485964330, "cpu": 0.0233, "memory": 0.10793060064315796, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749485966331, "cpu": 0.010799999999999999, "memory": 0.10075517930090427, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749485968332, "cpu": 0.0117, "memory": 0.1009221188724041, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749485970333, "cpu": 0.0125, "memory": 0.10272893123328686, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749485972334, "cpu": 0.013200000000000002, "memory": 0.11140820570290089, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749485974335, "cpu": 0.014100000000000001, "memory": 0.11515547521412373, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749485976336, "cpu": 0.0136, "memory": 0.10501635260879993, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749485978338, "cpu": 0.0138, "memory": 0.10554259642958641, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749485980339, "cpu": 0.0402, "memory": 0.10660449042916298, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749485982340, "cpu": 0.0123, "memory": 0.1061667688190937, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749485984341, "cpu": 0.0118, "memory": 0.10605067946016788, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749485986341, "cpu": 0.0387, "memory": 0.11315573938190937, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749485988343, "cpu": 0.011000000000000001, "memory": 0.10610767640173435, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749485990343, "cpu": 0.0069, "memory": 0.10750540532171726, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749485992343, "cpu": 0.0075, "memory": 0.10669832117855549, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749485994344, "cpu": 0.0073999999999999995, "memory": 0.10728058405220509, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749485996345, "cpu": 0.013, "memory": 0.11387607082724571, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749485998347, "cpu": 0.013999999999999999, "memory": 0.12059859000146389, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749486000353, "cpu": 0.618, "memory": 0.11460105888545513, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749486002348, "cpu": 0.018000000000000002, "memory": 0.11404906399548054, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749486004350, "cpu": 0.014100000000000001, "memory": 0.11397944763302803, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749486006351, "cpu": 0.0092, "memory": 0.1144979614764452, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749486008352, "cpu": 0.0101, "memory": 0.1149243675172329, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749486010352, "cpu": 0.0629, "memory": 0.11565601453185081, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749486012353, "cpu": 0.0084, "memory": 0.11519705876708031, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749486014354, "cpu": 0.0117, "memory": 0.1153651624917984, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749486016355, "cpu": 0.0143, "memory": 0.12555965222418308, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749486018355, "cpu": 0.014799999999999999, "memory": 0.11831820011138916, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749486020356, "cpu": 0.5655, "memory": 0.12023490853607655, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749486022357, "cpu": 0.0095, "memory": 0.12930585071444511, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749486024359, "cpu": 0.0144, "memory": 0.12214062735438347, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749486026360, "cpu": 0.0125, "memory": 0.12276927009224892, "temperature": 37, "efficiency": 65}, {"timestamp": 1749486028360, "cpu": 0.0077, "memory": 0.12331693433225155, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749486030362, "cpu": 0.053, "memory": 0.12490060180425644, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749486032362, "cpu": 0.0093, "memory": 0.13374360278248787, "temperature": 37, "efficiency": 65}, {"timestamp": 1749486034364, "cpu": 0.0071, "memory": 0.12626219540834427, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749486036364, "cpu": 0.012400000000000001, "memory": 0.12674611061811447, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749486038364, "cpu": 0.007600000000000001, "memory": 0.12721302919089794, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749486040365, "cpu": 0.6338, "memory": 0.12815697118639946, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749486042367, "cpu": 0.013, "memory": 0.12748781591653824, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749486044368, "cpu": 0.009000000000000001, "memory": 0.12789121828973293, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749486046369, "cpu": 0.0139, "memory": 0.1410044264048338, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749486048370, "cpu": 0.0082, "memory": 0.1338441390544176, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749486050372, "cpu": 0.6533, "memory": 0.1353282481431961, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749486052373, "cpu": 0.0126, "memory": 0.13467827811837196, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749486054374, "cpu": 0.0114, "memory": 0.13526012189686298, "temperature": 37, "efficiency": 64}, {"timestamp": 1749486056375, "cpu": 0.0127, "memory": 0.13500312343239784, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749486058376, "cpu": 0.0119, "memory": 0.13550282455980778, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749486060378, "cpu": 0.0553, "memory": 0.13708341866731644, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749486062380, "cpu": 0.0226, "memory": 0.1432953868061304, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749486064379, "cpu": 0.014100000000000001, "memory": 0.1359906978905201, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749486066380, "cpu": 0.012799999999999999, "memory": 0.13654883950948715, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749486068381, "cpu": 0.013200000000000002, "memory": 0.13681426644325256, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749486070383, "cpu": 0.0747, "memory": 0.1381188165396452, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749486072384, "cpu": 0.009899999999999999, "memory": 0.13750623911619186, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749486074385, "cpu": 0.0135, "memory": 0.1379360444843769, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749486076386, "cpu": 0.0111, "memory": 0.09949635714292526, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749486078387, "cpu": 0.0127, "memory": 0.09997142478823662, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749486080463, "cpu": 0.6842, "memory": 0.1010415144264698, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749486082388, "cpu": 0.0125, "memory": 0.11027841828763485, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749486084389, "cpu": 0.0063999999999999994, "memory": 0.10313205420970917, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749486086391, "cpu": 0.006600000000000001, "memory": 0.10371594689786434, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749486088392, "cpu": 0.0113, "memory": 0.10368875227868557, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749486090393, "cpu": 0.0626, "memory": 0.10530184954404831, "temperature": 37, "efficiency": 67}, {"timestamp": 1749486092394, "cpu": 0.006999999999999999, "memory": 0.10444261133670807, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749486094394, "cpu": 0.0102, "memory": 0.10425611399114132, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749486096394, "cpu": 0.0184, "memory": 0.10824515484273434, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749486098396, "cpu": 0.0072, "memory": 0.1151067204773426, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749486100397, "cpu": 0.6096, "memory": 0.10879253968596458, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749486102398, "cpu": 0.0127, "memory": 0.10840455070137978, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749486104399, "cpu": 0.012, "memory": 0.10876823216676712, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749486106400, "cpu": 0.0107, "memory": 0.11876001954078674, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749486108401, "cpu": 0.0107, "memory": 0.11929348111152649, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749486110402, "cpu": 0.0599, "memory": 0.11312663555145264, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749486112403, "cpu": 0.0156, "memory": 0.11242222972214222, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749486114404, "cpu": 0.0105, "memory": 0.1132600475102663, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749486116405, "cpu": 0.0103, "memory": 0.11277860030531883, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749486118407, "cpu": 0.009899999999999999, "memory": 0.1130515243858099, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749486120408, "cpu": 0.045899999999999996, "memory": 0.11475365608930588, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749486122409, "cpu": 0.0143, "memory": 0.12066964991390705, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749486124409, "cpu": 0.0127, "memory": 0.12371176853775978, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749486126410, "cpu": 0.0065, "memory": 0.11685476638376713, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749486128411, "cpu": 0.006200000000000001, "memory": 0.11724787764251232, "temperature": 37, "efficiency": 66}, {"timestamp": 1749486130412, "cpu": 0.666, "memory": 0.11838148348033428, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749486132413, "cpu": 0.0107, "memory": 0.1177358441054821, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749486134413, "cpu": 0.0068, "memory": 0.11828923597931862, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749486136414, "cpu": 0.006999999999999999, "memory": 0.12839040718972683, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749486138414, "cpu": 0.017, "memory": 0.12123449705541134, "temperature": 37, "efficiency": 66}, {"timestamp": 1749486140415, "cpu": 0.604, "memory": 0.12253038585186005, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749486142416, "cpu": 0.014899999999999998, "memory": 0.12181210331618786, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749486144417, "cpu": 0.0111, "memory": 0.12255306355655193, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749486146417, "cpu": 0.0127, "memory": 0.12241252698004246, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749486148418, "cpu": 0.0114, "memory": 0.12245462276041508, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749486150418, "cpu": 0.6292, "memory": 0.12395698577165604, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749486152418, "cpu": 0.051500000000000004, "memory": 0.129779614508152, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749486154427, "cpu": 0.0697, "memory": 0.13308287598192692, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749486156426, "cpu": 0.0291, "memory": 0.12609781697392464, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749486158427, "cpu": 0.040299999999999996, "memory": 0.12648687697947025, "temperature": 37, "efficiency": 65.4}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}