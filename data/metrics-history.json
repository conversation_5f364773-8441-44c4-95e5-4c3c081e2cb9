{"timestamp": 1749485435097, "metrics": {"system": {"cpu": {"usage": 0.0724, "temperature": 37, "cores": 10}, "memory": {"used": 17676560, "total": 17179869184, "efficiency": 24.50023781927922}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749484835076}, "performance": {"uptime": 598.468, "efficiency": 70.1, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749485235425, "cpu": 0.6554, "memory": 0.0900855753570795, "temperature": 37, "efficiency": 70.3}, {"timestamp": 1749485237425, "cpu": 0.0127, "memory": 0.08964007720351219, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749485239425, "cpu": 0.01, "memory": 0.08996627293527126, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749485241425, "cpu": 0.0065, "memory": 0.09407424367964268, "temperature": 37, "efficiency": 69.4}, {"timestamp": 1749485243427, "cpu": 0.0388, "memory": 0.10032122954726219, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749485245427, "cpu": 0.19910000000000003, "memory": 0.09710509330034256, "temperature": 37, "efficiency": 71}, {"timestamp": 1749485247429, "cpu": 0.06949999999999999, "memory": 0.10718428529798985, "temperature": 37, "efficiency": 68.5}, {"timestamp": 1749485249430, "cpu": 0.055900000000000005, "memory": 0.09722970426082611, "temperature": 37, "efficiency": 69.8}, {"timestamp": 1749485251431, "cpu": 0.0319, "memory": 0.09846556931734085, "temperature": 37, "efficiency": 69.5}, {"timestamp": 1749485253432, "cpu": 0.0567, "memory": 0.0977653544396162, "temperature": 37, "efficiency": 69.7}, {"timestamp": 1749485255432, "cpu": 0.1111, "memory": 0.09943568147718906, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749485257433, "cpu": 0.08549999999999999, "memory": 0.10796338319778442, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749485259434, "cpu": 0.0491, "memory": 0.11144522577524185, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749485261435, "cpu": 0.0574, "memory": 0.10535256005823612, "temperature": 37, "efficiency": 69}, {"timestamp": 1749485263436, "cpu": 0.09920000000000001, "memory": 0.10504866950213909, "temperature": 37, "efficiency": 69}, {"timestamp": 1749485265438, "cpu": 1.1872, "memory": 0.10600117966532707, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749485267438, "cpu": 0.0531, "memory": 0.10550539009273052, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749485269438, "cpu": 0.0416, "memory": 0.10579521767795086, "temperature": 37, "efficiency": 70}, {"timestamp": 1749485271439, "cpu": 0.0816, "memory": 0.10623247362673283, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749485273440, "cpu": 0.08499999999999999, "memory": 0.10652728378772736, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749485275442, "cpu": 0.148, "memory": 0.10762922465801239, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749485277442, "cpu": 0.0611, "memory": 0.1137507613748312, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749485279443, "cpu": 0.046, "memory": 0.10673869401216507, "temperature": 37, "efficiency": 69.8}, {"timestamp": 1749485281445, "cpu": 0.0786, "memory": 0.10798415169119835, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749485283445, "cpu": 0.0659, "memory": 0.1080663874745369, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749485285445, "cpu": 1.1991, "memory": 0.10973867028951645, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749485287447, "cpu": 0.06520000000000001, "memory": 0.12114811688661575, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749485289448, "cpu": 0.0676, "memory": 0.12465277686715126, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749485291449, "cpu": 0.0475, "memory": 0.11867983266711235, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749485293451, "cpu": 0.034499999999999996, "memory": 0.11819656938314438, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749485295454, "cpu": 0.7155, "memory": 0.1196705736219883, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749485297452, "cpu": 0.0664, "memory": 0.1223184634000063, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749485299454, "cpu": 0.0386, "memory": 0.1292545348405838, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749485301454, "cpu": 0.093, "memory": 0.12237848713994026, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749485303455, "cpu": 0.0487, "memory": 0.12274137698113918, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749485305461, "cpu": 1.2381, "memory": 0.1239115372300148, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749485307457, "cpu": 0.0523, "memory": 0.12306789867579937, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749485309459, "cpu": 0.062299999999999994, "memory": 0.1236338634043932, "temperature": 37, "efficiency": 67}, {"timestamp": 1749485311461, "cpu": 0.0457, "memory": 0.12450432404875755, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749485313462, "cpu": 0.0394, "memory": 0.12383665889501572, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749485315462, "cpu": 1.3191, "memory": 0.12535881251096725, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749485317463, "cpu": 0.0508, "memory": 0.12428080663084984, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749485319465, "cpu": 0.09759999999999999, "memory": 0.12451992370188236, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749485321465, "cpu": 0.10039999999999999, "memory": 0.12637902982532978, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749485323466, "cpu": 0.083, "memory": 0.13231621123850346, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749485325467, "cpu": 1.0693, "memory": 0.12620314955711365, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749485327469, "cpu": 0.0479, "memory": 0.1257164403796196, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749485329470, "cpu": 0.0333, "memory": 0.12592170387506485, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749485331471, "cpu": 0.061799999999999994, "memory": 0.12628124095499516, "temperature": 37, "efficiency": 67}, {"timestamp": 1749485333472, "cpu": 0.051199999999999996, "memory": 0.12660636566579342, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749485335475, "cpu": 1.3103, "memory": 0.12759496457874775, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749485337478, "cpu": 0.0601, "memory": 0.12701223604381084, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749485339482, "cpu": 0.0548, "memory": 0.1275793183594942, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749485341482, "cpu": 0.053399999999999996, "memory": 0.1285783015191555, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749485343485, "cpu": 0.0552, "memory": 0.1276138238608837, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749485345488, "cpu": 1.2839, "memory": 0.12965044006705284, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749485347489, "cpu": 0.11030000000000001, "memory": 0.08836481720209122, "temperature": 37, "efficiency": 71.6}, {"timestamp": 1749485349491, "cpu": 0.10729999999999999, "memory": 0.08914312347769737, "temperature": 37, "efficiency": 71.3}, {"timestamp": 1749485351493, "cpu": 0.0892, "memory": 0.09154346771538258, "temperature": 37, "efficiency": 70.7}, {"timestamp": 1749485353494, "cpu": 0.0462, "memory": 0.10284446179866791, "temperature": 37, "efficiency": 70.7}, {"timestamp": 1749485355494, "cpu": 1.1298000000000001, "memory": 0.09940429590642452, "temperature": 37, "efficiency": 71.2}, {"timestamp": 1749485357495, "cpu": 0.0784, "memory": 0.09933477267622948, "temperature": 37, "efficiency": 70.5}, {"timestamp": 1749485359498, "cpu": 0.06080000000000001, "memory": 0.10023219510912895, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749485361498, "cpu": 0.0905, "memory": 0.10662293061614037, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749485363500, "cpu": 0.0556, "memory": 0.11316975578665733, "temperature": 37, "efficiency": 69.9}, {"timestamp": 1749485365500, "cpu": 1.7634, "memory": 0.10985769331455231, "temperature": 37, "efficiency": 70.1}, {"timestamp": 1749485367501, "cpu": 0.2003, "memory": 0.12016994878649712, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749485369503, "cpu": 0.0701, "memory": 0.1107931137084961, "temperature": 37, "efficiency": 69.4}, {"timestamp": 1749485371504, "cpu": 0.0854, "memory": 0.11139209382236004, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749485373506, "cpu": 0.07429999999999999, "memory": 0.11050812900066376, "temperature": 37, "efficiency": 69.4}, {"timestamp": 1749485375507, "cpu": 2.2183, "memory": 0.11200765147805214, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749485377507, "cpu": 0.066, "memory": 0.11111060157418251, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749485379509, "cpu": 0.0877, "memory": 0.11136950924992561, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749485381509, "cpu": 0.05959999999999999, "memory": 0.112964678555727, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749485383509, "cpu": 0.0535, "memory": 0.11221175082027912, "temperature": 37, "efficiency": 69}, {"timestamp": 1749485385509, "cpu": 1.0737999999999999, "memory": 0.11288761161267757, "temperature": 37, "efficiency": 68.5}, {"timestamp": 1749485387510, "cpu": 0.0643, "memory": 0.1123668160289526, "temperature": 37, "efficiency": 69}, {"timestamp": 1749485389511, "cpu": 0.0884, "memory": 0.11261231265962124, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749485391511, "cpu": 0.1112, "memory": 0.116360979154706, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749485393514, "cpu": 0.058699999999999995, "memory": 0.12293276377022266, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749485395515, "cpu": 1.4583000000000002, "memory": 0.11986088939011097, "temperature": 37, "efficiency": 69}, {"timestamp": 1749485397516, "cpu": 0.1669, "memory": 0.130010349676013, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749485399518, "cpu": 0.07690000000000001, "memory": 0.11991127394139767, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749485401518, "cpu": 0.1106, "memory": 0.1211114227771759, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749485403519, "cpu": 0.107, "memory": 0.12041139416396618, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749485405520, "cpu": 1.2066999999999999, "memory": 0.12196642346680164, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749485407520, "cpu": 0.124, "memory": 0.12093274854123592, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749485409522, "cpu": 0.1205, "memory": 0.12108925729990005, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749485411524, "cpu": 0.11199999999999999, "memory": 0.12287595309317112, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749485413524, "cpu": 0.0324, "memory": 0.13196486979722977, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749485415535, "cpu": 0.161, "memory": 0.12586289085447788, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749485417536, "cpu": 0.0886, "memory": 0.12545818462967873, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749485419536, "cpu": 0.0663, "memory": 0.12591052800416946, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749485421538, "cpu": 0.0939, "memory": 0.13252203352749348, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749485423539, "cpu": 0.0798, "memory": 0.13894164003431797, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749485425539, "cpu": 0.1163, "memory": 0.08994885720312595, "temperature": 37, "efficiency": 71.7}, {"timestamp": 1749485427541, "cpu": 0.1018, "memory": 0.09928946383297443, "temperature": 37, "efficiency": 70.5}, {"timestamp": 1749485429541, "cpu": 0.0887, "memory": 0.09236661717295647, "temperature": 37, "efficiency": 70.5}, {"timestamp": 1749485431543, "cpu": 0.0729, "memory": 0.09396485984325409, "temperature": 37, "efficiency": 70.1}, {"timestamp": 1749485433544, "cpu": 0.0724, "memory": 0.10289112105965614, "temperature": 37, "efficiency": 70.1}], "alerts": [], "optimizations": [{"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}