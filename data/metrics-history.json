{"timestamp": 1749479742982, "metrics": {"system": {"cpu": {"usage": 0.0121, "temperature": 37, "cores": 10}, "memory": {"used": 17868496, "total": 17179869184, "efficiency": 8.429349679890848}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749479442978}, "performance": {"uptime": 298.483, "efficiency": 64.8, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 99, "connections": 0}}, "history": [{"timestamp": 1749479543135, "cpu": 0.8406, "memory": 0.08498942479491234, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749479545137, "cpu": 0.5693, "memory": 0.08239853195846081, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749479547137, "cpu": 0.06080000000000001, "memory": 0.08438564836978912, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749479549138, "cpu": 0.06369999999999999, "memory": 0.08607711642980576, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749479551243, "cpu": 0.488, "memory": 0.08349050767719746, "temperature": 37, "efficiency": 65}, {"timestamp": 1749479553162, "cpu": 0.0835, "memory": 0.0857371836900711, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749479555163, "cpu": 0.0505, "memory": 0.08436632342636585, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749479557166, "cpu": 0.0688, "memory": 0.08492260240018368, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749479559167, "cpu": 0.1396, "memory": 0.08691931143403053, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749479561168, "cpu": 0.049600000000000005, "memory": 0.08555636741220951, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749479563170, "cpu": 0.6713, "memory": 0.08642771281301975, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749479565168, "cpu": 0.168, "memory": 0.084687490016222, "temperature": 37, "efficiency": 67}, {"timestamp": 1749479567176, "cpu": 0.1449, "memory": 0.08557690307497978, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749479569171, "cpu": 0.11249999999999999, "memory": 0.08717235177755356, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749479571173, "cpu": 0.1749, "memory": 0.0857069157063961, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749479573173, "cpu": 0.2523, "memory": 0.08778879418969154, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749479575174, "cpu": 0.1925, "memory": 0.0893913209438324, "temperature": 37, "efficiency": 64}, {"timestamp": 1749479577174, "cpu": 0.09380000000000001, "memory": 0.08664736524224281, "temperature": 37, "efficiency": 65}, {"timestamp": 1749479579175, "cpu": 0.057300000000000004, "memory": 0.08848770521581173, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749479581175, "cpu": 0.12390000000000001, "memory": 0.09004627354443073, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749479583176, "cpu": 0.1106, "memory": 0.08807824924588203, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749479585177, "cpu": 0.1292, "memory": 0.09057619608938694, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749479587177, "cpu": 0.4114, "memory": 0.08722026832401752, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749479589182, "cpu": 0.091, "memory": 0.08882670663297176, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749479591182, "cpu": 0.1913, "memory": 0.09131911210715771, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749479593181, "cpu": 0.6815, "memory": 0.08828043937683105, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749479595182, "cpu": 0.078, "memory": 0.09071426466107368, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749479597184, "cpu": 0.12489999999999998, "memory": 0.08815978653728962, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749479599184, "cpu": 0.0799, "memory": 0.0888975802809, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749479601186, "cpu": 0.0754, "memory": 0.09146947413682938, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749479603329, "cpu": 0.3406, "memory": 0.0888175331056118, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749479605331, "cpu": 0.1624, "memory": 0.09033801034092903, "temperature": 37, "efficiency": 66}, {"timestamp": 1749479607331, "cpu": 0.07730000000000001, "memory": 0.09187520481646061, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749479609332, "cpu": 0.1057, "memory": 0.08939332328736782, "temperature": 37, "efficiency": 65}, {"timestamp": 1749479611332, "cpu": 0.11100000000000002, "memory": 0.09091533720493317, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749479613333, "cpu": 0.0816, "memory": 0.09261248633265495, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749479615335, "cpu": 0.0552, "memory": 0.09095543064177036, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749479617336, "cpu": 0.1655, "memory": 0.09155371226370335, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749479619336, "cpu": 0.0785, "memory": 0.0930876936763525, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749479621338, "cpu": 0.0815, "memory": 0.09149122051894665, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749479623339, "cpu": 0.0702, "memory": 0.09226012043654919, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749479625340, "cpu": 0.0522, "memory": 0.09072837419807911, "temperature": 37, "efficiency": 65}, {"timestamp": 1749479627343, "cpu": 0.6308, "memory": 0.09222445078194141, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749479629343, "cpu": 0.129, "memory": 0.09311642497777939, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749479631344, "cpu": 0.12719999999999998, "memory": 0.09150146506726742, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749479633345, "cpu": 0.20980000000000001, "memory": 0.09311572648584843, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749479635348, "cpu": 0.6011, "memory": 0.09478218853473663, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749479637350, "cpu": 0.5046, "memory": 0.09199553169310093, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749479639349, "cpu": 0.3147, "memory": 0.09363805875182152, "temperature": 37, "efficiency": 64}, {"timestamp": 1749479641351, "cpu": 0.1938, "memory": 0.0951363705098629, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749479643351, "cpu": 0.1426, "memory": 0.09263777174055576, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749479645352, "cpu": 0.1031, "memory": 0.09516305290162563, "temperature": 37, "efficiency": 64}, {"timestamp": 1749479647353, "cpu": 1.5235, "memory": 0.09576138108968735, "temperature": 37, "efficiency": 63.3}, {"timestamp": 1749479649355, "cpu": 0.4882, "memory": 0.09326445870101452, "temperature": 37, "efficiency": 67}, {"timestamp": 1749479651355, "cpu": 0.5091, "memory": 0.09664581157267094, "temperature": 37, "efficiency": 66}, {"timestamp": 1749479653357, "cpu": 0.0903, "memory": 0.09394362568855286, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749479655359, "cpu": 0.5064, "memory": 0.09637689217925072, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749479657361, "cpu": 0.2576, "memory": 0.09798714891076088, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749479659361, "cpu": 0.0935, "memory": 0.09446605108678341, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749479661362, "cpu": 0.27959999999999996, "memory": 0.09690071456134319, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749479663364, "cpu": 0.0818, "memory": 0.09847818873822689, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749479665365, "cpu": 0.0767, "memory": 0.09616818279027939, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749479667365, "cpu": 0.189, "memory": 0.09859036654233932, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749479669367, "cpu": 0.178, "memory": 0.09588440880179405, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749479671367, "cpu": 0.0843, "memory": 0.09747347794473171, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749479673369, "cpu": 0.0706, "memory": 0.09931866079568863, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749479675369, "cpu": 0.0821, "memory": 0.09729019366204739, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749479677374, "cpu": 0.2525, "memory": 0.0978201162070036, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749479679372, "cpu": 0.0756, "memory": 0.09964779019355774, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749479681373, "cpu": 0.0794, "memory": 0.09824126027524471, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749479683373, "cpu": 0.0658, "memory": 0.09920191951096058, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749479685376, "cpu": 0.0841, "memory": 0.09754691272974014, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749479687377, "cpu": 0.0719, "memory": 0.09917491115629673, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749479689378, "cpu": 0.08099999999999999, "memory": 0.09988979436457157, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749479691605, "cpu": 0.9839, "memory": 0.09819641709327698, "temperature": 37, "efficiency": 65}, {"timestamp": 1749479693425, "cpu": 0.079, "memory": 0.1000676304101944, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749479695425, "cpu": 0.0457, "memory": 0.10156421922147274, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749479697426, "cpu": 0.24810000000000001, "memory": 0.09895297698676586, "temperature": 37, "efficiency": 65}, {"timestamp": 1749479699427, "cpu": 0.0759, "memory": 0.10064477100968361, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749479701441, "cpu": 0.0817, "memory": 0.10220860131084919, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749479703443, "cpu": 0.0757, "memory": 0.10040830820798874, "temperature": 37, "efficiency": 65}, {"timestamp": 1749479705442, "cpu": 0.0344, "memory": 0.10287291370332241, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749479707442, "cpu": 0.0279, "memory": 0.10339980944991112, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749479709443, "cpu": 0.0433, "memory": 0.10098456405103207, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749479711445, "cpu": 0.13060000000000002, "memory": 0.10347375646233559, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749479713445, "cpu": 0.051199999999999996, "memory": 0.10011009871959686, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749479715445, "cpu": 0.053700000000000005, "memory": 0.10262266732752323, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749479717448, "cpu": 0.0427, "memory": 0.10411757975816727, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749479719448, "cpu": 0.0278, "memory": 0.10061971843242645, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749479721451, "cpu": 0.0639, "memory": 0.10307319462299347, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749479723451, "cpu": 0.0164, "memory": 0.10493858717381954, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749479725453, "cpu": 0.1015, "memory": 0.10244003497064114, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749479727454, "cpu": 0.0127, "memory": 0.10414468124508858, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749479729454, "cpu": 0.0072, "memory": 0.10205423459410667, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749479731455, "cpu": 0.0126, "memory": 0.10359296575188637, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749479733456, "cpu": 0.012799999999999999, "memory": 0.10536899790167809, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749479735457, "cpu": 0.0395, "memory": 0.10370789095759392, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749479737459, "cpu": 0.014799999999999999, "memory": 0.10420437902212143, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749479739460, "cpu": 0.0211, "memory": 0.10568918660283089, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749479741461, "cpu": 0.0121, "memory": 0.10400833562016487, "temperature": 37, "efficiency": 64.8}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}