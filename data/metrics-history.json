{"timestamp": 1749472412785, "metrics": {"system": {"cpu": {"usage": 0.0086, "temperature": 37, "cores": 10}, "memory": {"used": 21187880, "total": 17179869184, "efficiency": 11.241896609042556}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749471812791}, "performance": {"uptime": 598.433, "efficiency": 65.7, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749472213123, "cpu": 0.009000000000000001, "memory": 0.09507406502962112, "temperature": 37, "efficiency": 72.5}, {"timestamp": 1749472215123, "cpu": 0.007600000000000001, "memory": 0.09339125826954842, "temperature": 37, "efficiency": 71.9}, {"timestamp": 1749472217125, "cpu": 0.0095, "memory": 0.09579653851687908, "temperature": 37, "efficiency": 71.3}, {"timestamp": 1749472219125, "cpu": 0.0095, "memory": 0.0929598230868578, "temperature": 37, "efficiency": 72}, {"timestamp": 1749472221127, "cpu": 0.015200000000000002, "memory": 0.09527308866381645, "temperature": 37, "efficiency": 71.4}, {"timestamp": 1749472223127, "cpu": 0.0086, "memory": 0.09729410521686077, "temperature": 37, "efficiency": 70.9}, {"timestamp": 1749472225129, "cpu": 0.0112, "memory": 0.09408099576830864, "temperature": 37, "efficiency": 71.7}, {"timestamp": 1749472227129, "cpu": 0.0073, "memory": 0.09710551239550114, "temperature": 37, "efficiency": 71}, {"timestamp": 1749472229130, "cpu": 0.1028, "memory": 0.09455005638301373, "temperature": 37, "efficiency": 71.6}, {"timestamp": 1749472231131, "cpu": 0.02, "memory": 0.09693126194179058, "temperature": 37, "efficiency": 71}, {"timestamp": 1749472233133, "cpu": 0.0082, "memory": 0.09537548758089542, "temperature": 37, "efficiency": 71.4}, {"timestamp": 1749472235134, "cpu": 0.0071, "memory": 0.09631919674575329, "temperature": 37, "efficiency": 71.2}, {"timestamp": 1749472237135, "cpu": 0.0106, "memory": 0.09895297698676586, "temperature": 37, "efficiency": 70.5}, {"timestamp": 1749472239136, "cpu": 0.0105, "memory": 0.09668269194662571, "temperature": 37, "efficiency": 72.1}, {"timestamp": 1749472241137, "cpu": 0.0067, "memory": 0.09827162139117718, "temperature": 37, "efficiency": 71.8}, {"timestamp": 1749472243138, "cpu": 0.0072, "memory": 0.10024779476225376, "temperature": 37, "efficiency": 71.3}, {"timestamp": 1749472245139, "cpu": 0.0131, "memory": 0.09860857389867306, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749472247140, "cpu": 0.0073, "memory": 0.09714118205010891, "temperature": 37, "efficiency": 71}, {"timestamp": 1749472249141, "cpu": 0.0166, "memory": 0.09841038845479488, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749472251141, "cpu": 0.0085, "memory": 0.10087210685014725, "temperature": 37, "efficiency": 70}, {"timestamp": 1749472253142, "cpu": 0.0138, "memory": 0.09888359345495701, "temperature": 37, "efficiency": 70.5}, {"timestamp": 1749472255144, "cpu": 0.016300000000000002, "memory": 0.10007848031818867, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749472257144, "cpu": 0.013300000000000001, "memory": 0.09960439056158066, "temperature": 37, "efficiency": 70.3}, {"timestamp": 1749472259146, "cpu": 0.007899999999999999, "memory": 0.10109548456966877, "temperature": 37, "efficiency": 70}, {"timestamp": 1749472261148, "cpu": 0.05159999999999999, "memory": 0.1028275117278099, "temperature": 37, "efficiency": 69.5}, {"timestamp": 1749472263148, "cpu": 0.0119, "memory": 0.10197246447205544, "temperature": 37, "efficiency": 69.7}, {"timestamp": 1749472265149, "cpu": 0.009899999999999999, "memory": 0.10339329019188881, "temperature": 37, "efficiency": 69.4}, {"timestamp": 1749472267151, "cpu": 0.007600000000000001, "memory": 0.1019373070448637, "temperature": 37, "efficiency": 70.9}, {"timestamp": 1749472269152, "cpu": 0.0127, "memory": 0.10381005704402924, "temperature": 37, "efficiency": 70.4}, {"timestamp": 1749472271153, "cpu": 0.0101, "memory": 0.10224897414445877, "temperature": 37, "efficiency": 69.7}, {"timestamp": 1749472273155, "cpu": 0.0102, "memory": 0.10396689176559448, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749472275156, "cpu": 0.0136, "memory": 0.10200641117990017, "temperature": 37, "efficiency": 69.7}, {"timestamp": 1749472277157, "cpu": 0.014799999999999999, "memory": 0.10436838492751122, "temperature": 37, "efficiency": 69.1}, {"timestamp": 1749472279157, "cpu": 0.0111, "memory": 0.10542511008679867, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749472281159, "cpu": 0.0069, "memory": 0.10410989634692669, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749472283160, "cpu": 0.0116, "memory": 0.10579456575214863, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749472285160, "cpu": 0.0106, "memory": 0.1031283289194107, "temperature": 37, "efficiency": 69.5}, {"timestamp": 1749472287161, "cpu": 0.0121, "memory": 0.10630888864398003, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749472289166, "cpu": 0.0092, "memory": 0.10340767912566662, "temperature": 37, "efficiency": 69.4}, {"timestamp": 1749472291168, "cpu": 0.0084, "memory": 0.10510943830013275, "temperature": 37, "efficiency": 69}, {"timestamp": 1749472293169, "cpu": 0.007600000000000001, "memory": 0.10407776571810246, "temperature": 37, "efficiency": 70.4}, {"timestamp": 1749472295170, "cpu": 0.0117, "memory": 0.10526799596846104, "temperature": 37, "efficiency": 70.1}, {"timestamp": 1749472297171, "cpu": 0.0098, "memory": 0.10771919041872025, "temperature": 37, "efficiency": 69.5}, {"timestamp": 1749472299171, "cpu": 0.0112, "memory": 0.10567735880613327, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749472301172, "cpu": 0.1525, "memory": 0.10735359974205494, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749472303174, "cpu": 0.0139, "memory": 0.10903961956501007, "temperature": 37, "efficiency": 68}, {"timestamp": 1749472305174, "cpu": 0.0093, "memory": 0.10720905847847462, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749472307175, "cpu": 0.1308, "memory": 0.10966057889163494, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749472309176, "cpu": 0.0113, "memory": 0.10671564377844334, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749472311177, "cpu": 0.10660000000000001, "memory": 0.10926267132163048, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749472313178, "cpu": 0.009899999999999999, "memory": 0.10731103830039501, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749472315180, "cpu": 0.0113, "memory": 0.10849717073142529, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749472317181, "cpu": 0.0155, "memory": 0.10756785050034523, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749472319182, "cpu": 0.008, "memory": 0.10888734832406044, "temperature": 37, "efficiency": 68}, {"timestamp": 1749472321183, "cpu": 0.0111, "memory": 0.11129737831652164, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749472323183, "cpu": 0.012, "memory": 0.10948539711534977, "temperature": 37, "efficiency": 69.1}, {"timestamp": 1749472325184, "cpu": 0.08270000000000001, "memory": 0.11077974922955036, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749472327186, "cpu": 0.017499999999999998, "memory": 0.10962667874991894, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749472329187, "cpu": 0.0155, "memory": 0.11152494698762894, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749472331188, "cpu": 0.0184, "memory": 0.11320095509290695, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749472333189, "cpu": 0.0154, "memory": 0.11112354695796967, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749472335190, "cpu": 0.010799999999999999, "memory": 0.11315243318676949, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749472337191, "cpu": 0.10790000000000001, "memory": 0.11172741651535034, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749472339191, "cpu": 0.012199999999999999, "memory": 0.11285948567092419, "temperature": 37, "efficiency": 67}, {"timestamp": 1749472341193, "cpu": 0.0084, "memory": 0.11234767735004425, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749472343194, "cpu": 0.008, "memory": 0.11383029632270336, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749472345193, "cpu": 0.008, "memory": 0.11487309820950031, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749472347196, "cpu": 0.0077, "memory": 0.11399867944419384, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749472349199, "cpu": 0.018699999999999998, "memory": 0.11504786089062691, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749472351198, "cpu": 0.0172, "memory": 0.11305646039545536, "temperature": 37, "efficiency": 68.2}, {"timestamp": 1749472353198, "cpu": 0.0106, "memory": 0.11549722403287888, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749472355199, "cpu": 0.16219999999999998, "memory": 0.11652233079075813, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749472357201, "cpu": 0.009600000000000001, "memory": 0.11498508974909782, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749472359202, "cpu": 0.0084, "memory": 0.11677122674882412, "temperature": 37, "efficiency": 66}, {"timestamp": 1749472361202, "cpu": 0.20040000000000002, "memory": 0.11456138454377651, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749472363203, "cpu": 0.009000000000000001, "memory": 0.11625569313764572, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749472365205, "cpu": 0.0102, "memory": 0.11796355247497559, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749472367205, "cpu": 0.0126, "memory": 0.11663949117064476, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749472369206, "cpu": 0.0104, "memory": 0.11776820756494999, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749472371207, "cpu": 0.0075, "memory": 0.11627506464719772, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749472373207, "cpu": 0.0085, "memory": 0.11893529444932938, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749472375208, "cpu": 0.0119, "memory": 0.11940388940274715, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749472377209, "cpu": 0.0081, "memory": 0.11886078864336014, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749472379210, "cpu": 0.0089, "memory": 0.11990084312856197, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749472381211, "cpu": 0.009600000000000001, "memory": 0.11838502250611782, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749472383211, "cpu": 0.0137, "memory": 0.12048650532960892, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749472385213, "cpu": 0.009399999999999999, "memory": 0.11869138106703758, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749472387214, "cpu": 0.0088, "memory": 0.12037847191095352, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749472389215, "cpu": 0.0065, "memory": 0.11782720685005188, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749472391216, "cpu": 0.008, "memory": 0.11976337991654873, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749472393217, "cpu": 0.009399999999999999, "memory": 0.12167207896709442, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749472395218, "cpu": 0.0106, "memory": 0.11963634751737118, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749472397219, "cpu": 0.0134, "memory": 0.12270314618945122, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749472399220, "cpu": 0.0109, "memory": 0.11948826722800732, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749472401221, "cpu": 0.0063999999999999994, "memory": 0.12193149887025356, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749472403221, "cpu": 0.009899999999999999, "memory": 0.11989991180598736, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749472405222, "cpu": 0.006999999999999999, "memory": 0.12100054882466793, "temperature": 37, "efficiency": 65}, {"timestamp": 1749472407222, "cpu": 0.0115, "memory": 0.12417449615895748, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749472409223, "cpu": 0.0117, "memory": 0.12157964520156384, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749472411224, "cpu": 0.0086, "memory": 0.12332969345152378, "temperature": 37, "efficiency": 65.7}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}