{"timestamp": 1749472112794, "metrics": {"system": {"cpu": {"usage": 0.0147, "temperature": 37, "cores": 10}, "memory": {"used": 20665024, "total": 17179869184, "efficiency": 7.326012582659814}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749471812791}, "performance": {"uptime": 298.29, "efficiency": 64.4, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 99, "connections": 0}}, "history": [{"timestamp": 1749471912973, "cpu": 0.0523, "memory": 0.08660084567964077, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749471914973, "cpu": 0.1006, "memory": 0.08872882463037968, "temperature": 37, "efficiency": 66}, {"timestamp": 1749471916975, "cpu": 0.0672, "memory": 0.08763400837779045, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749471918976, "cpu": 0.123, "memory": 0.08880728855729103, "temperature": 37, "efficiency": 64}, {"timestamp": 1749471920976, "cpu": 0.0414, "memory": 0.08756914176046848, "temperature": 37, "efficiency": 65}, {"timestamp": 1749471922977, "cpu": 0.0749, "memory": 0.08961344137787819, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749471924977, "cpu": 0.1207, "memory": 0.08674003183841705, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749471926979, "cpu": 0.1263, "memory": 0.09034434333443642, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749471928979, "cpu": 0.0684, "memory": 0.08785338141024113, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749471930980, "cpu": 0.06939999999999999, "memory": 0.08989125490188599, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749471932980, "cpu": 0.0802, "memory": 0.08877548389136791, "temperature": 37, "efficiency": 65}, {"timestamp": 1749471934983, "cpu": 0.0408, "memory": 0.08993777446448803, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749471936983, "cpu": 0.0872, "memory": 0.08842162787914276, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749471938983, "cpu": 0.1361, "memory": 0.09045409969985485, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749471940984, "cpu": 0.05330000000000001, "memory": 0.09247045964002609, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749471942985, "cpu": 0.0772, "memory": 0.09055626578629017, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749471944986, "cpu": 0.0981, "memory": 0.0925254076719284, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749471946987, "cpu": 0.078, "memory": 0.09185695089399815, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749471948987, "cpu": 0.0702, "memory": 0.09303214028477669, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749471950988, "cpu": 0.0702, "memory": 0.09182835929095745, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749471952988, "cpu": 0.1052, "memory": 0.09382474236190319, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749471954990, "cpu": 0.1647, "memory": 0.09507723152637482, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749471956992, "cpu": 0.24180000000000001, "memory": 0.0946246087551117, "temperature": 37, "efficiency": 64}, {"timestamp": 1749471958992, "cpu": 0.1119, "memory": 0.09197592735290527, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749471960992, "cpu": 0.0451, "memory": 0.09393668733537197, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749471962993, "cpu": 0.055400000000000005, "memory": 0.09293407201766968, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749471964993, "cpu": 0.06999999999999999, "memory": 0.0940915197134018, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749471966993, "cpu": 0.0443, "memory": 0.09678187780082226, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749471968995, "cpu": 0.0605, "memory": 0.09480705484747887, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749471970995, "cpu": 0.0599, "memory": 0.09684725664556026, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749471972994, "cpu": 0.0468, "memory": 0.09480705484747887, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749471974995, "cpu": 0.0787, "memory": 0.09679878130555153, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749471976996, "cpu": 0.06130000000000001, "memory": 0.09559732861816883, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749471978998, "cpu": 0.06949999999999999, "memory": 0.09676492772996426, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749471980998, "cpu": 0.0644, "memory": 0.0956136267632246, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749471982999, "cpu": 0.0845, "memory": 0.09771748445928097, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749471984999, "cpu": 0.0696, "memory": 0.09901290759444237, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749471987002, "cpu": 0.2127, "memory": 0.09865514002740383, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749471989002, "cpu": 0.107, "memory": 0.09988993406295776, "temperature": 37, "efficiency": 63.2}, {"timestamp": 1749471991002, "cpu": 0.0737, "memory": 0.09801406413316727, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749471993004, "cpu": 0.0747, "memory": 0.10086535476148129, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749471995005, "cpu": 0.0804, "memory": 0.0982845202088356, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749471997006, "cpu": 0.1077, "memory": 0.10093124583363533, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749471999007, "cpu": 0.09079999999999999, "memory": 0.09908368811011314, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749472001009, "cpu": 0.0983, "memory": 0.10102866217494011, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749472003010, "cpu": 0.0861, "memory": 0.09921235032379627, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749472005012, "cpu": 0.20950000000000002, "memory": 0.10120309889316559, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749472007013, "cpu": 0.2703, "memory": 0.10010129772126675, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749472009015, "cpu": 0.09129999999999999, "memory": 0.10156193748116493, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749472011016, "cpu": 0.0916, "memory": 0.10030386038124561, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749472013017, "cpu": 0.0829, "memory": 0.10266955941915512, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749472015019, "cpu": 0.0692, "memory": 0.10398710146546364, "temperature": 37, "efficiency": 63.3}, {"timestamp": 1749472017020, "cpu": 0.0839, "memory": 0.10356977581977844, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749472019020, "cpu": 0.1616, "memory": 0.10502315126359463, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749472021022, "cpu": 0.0949, "memory": 0.10334346443414688, "temperature": 37, "efficiency": 64}, {"timestamp": 1749472023024, "cpu": 0.1885, "memory": 0.10619186796247959, "temperature": 37, "efficiency": 63.1}, {"timestamp": 1749472025023, "cpu": 0.0854, "memory": 0.10361149907112122, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749472027024, "cpu": 0.10640000000000001, "memory": 0.10622008703649044, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749472029025, "cpu": 0.0952, "memory": 0.10432135313749313, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749472031027, "cpu": 0.6142, "memory": 0.10613338090479374, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749472033028, "cpu": 0.1434, "memory": 0.10450021363794804, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749472035030, "cpu": 0.0814, "memory": 0.10649631731212139, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749472037031, "cpu": 0.3464, "memory": 0.10489490814507008, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749472039032, "cpu": 0.12789999999999999, "memory": 0.10616476647555828, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749472041033, "cpu": 0.08270000000000001, "memory": 0.1087353564798832, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749472043033, "cpu": 0.1337, "memory": 0.10712477378547192, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749472045035, "cpu": 0.053200000000000004, "memory": 0.10822736658155918, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749472047034, "cpu": 0.1157, "memory": 0.1073810737580061, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749472049036, "cpu": 0.08549999999999999, "memory": 0.10850620456039906, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749472051038, "cpu": 0.08059999999999999, "memory": 0.11047269217669964, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749472053038, "cpu": 0.0634, "memory": 0.10976772755384445, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749472055040, "cpu": 0.0936, "memory": 0.11100396513938904, "temperature": 37, "efficiency": 65}, {"timestamp": 1749472057041, "cpu": 0.1112, "memory": 0.1096096821129322, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749472059041, "cpu": 0.0638, "memory": 0.11152378283441067, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749472061044, "cpu": 0.1238, "memory": 0.10958448983728886, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749472063045, "cpu": 0.16390000000000002, "memory": 0.11147437617182732, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749472065077, "cpu": 0.133, "memory": 0.11300412006676197, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749472067047, "cpu": 0.18610000000000002, "memory": 0.11175763793289661, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749472069047, "cpu": 0.1676, "memory": 0.1129236537963152, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749472071049, "cpu": 0.1266, "memory": 0.11197002604603767, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749472073062, "cpu": 0.3683, "memory": 0.11380850337445736, "temperature": 37, "efficiency": 66}, {"timestamp": 1749472075064, "cpu": 0.030699999999999998, "memory": 0.11489572934806347, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749472077065, "cpu": 0.055, "memory": 0.11415630578994751, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749472079064, "cpu": 0.056999999999999995, "memory": 0.11527081951498985, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749472081067, "cpu": 0.074, "memory": 0.11302735656499863, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749472083067, "cpu": 0.0444, "memory": 0.1156891230493784, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749472085067, "cpu": 0.1594, "memory": 0.11278805322945118, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749472087069, "cpu": 0.0576, "memory": 0.11578951962292194, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749472089070, "cpu": 0.0402, "memory": 0.11423258110880852, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749472091071, "cpu": 0.032600000000000004, "memory": 0.11597811244428158, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749472093072, "cpu": 0.0327, "memory": 0.11790231801569462, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749472095072, "cpu": 0.037399999999999996, "memory": 0.115937739610672, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749472097073, "cpu": 0.029799999999999997, "memory": 0.11842506937682629, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749472099074, "cpu": 0.0245, "memory": 0.11579645797610283, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749472101078, "cpu": 1.0823, "memory": 0.11840309016406536, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749472103080, "cpu": 0.0967, "memory": 0.11667436920106411, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749472105078, "cpu": 0.0545, "memory": 0.11776541359722614, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749472107079, "cpu": 0.038400000000000004, "memory": 0.11729788966476917, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749472109080, "cpu": 0.0199, "memory": 0.11851689778268337, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749472111081, "cpu": 0.0147, "memory": 0.12028627097606659, "temperature": 37, "efficiency": 64.4}], "alerts": [], "optimizations": [{"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}