{"timestamp": 1749481617558, "metrics": {"system": {"cpu": {"usage": 0.5971, "temperature": 37, "cores": 10}, "memory": {"used": 21354832, "total": 17179869184, "efficiency": 5.207599431818181}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749481317527}, "performance": {"uptime": 299.604, "efficiency": 63.5, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 99, "connections": 0}}, "history": [{"timestamp": 1749481417700, "cpu": 0.013300000000000001, "memory": 0.10022842325270176, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749481419701, "cpu": 0.0181, "memory": 0.09892634116113186, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749481421702, "cpu": 0.0177, "memory": 0.1016030553728342, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749481423702, "cpu": 0.0114, "memory": 0.10333005338907242, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749481425704, "cpu": 0.0095, "memory": 0.10433811694383621, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749481427705, "cpu": 0.009600000000000001, "memory": 0.1053535845130682, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749481429705, "cpu": 0.0531, "memory": 0.10287314653396606, "temperature": 37, "efficiency": 65}, {"timestamp": 1749481431707, "cpu": 0.027, "memory": 0.10340861044824123, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749481433708, "cpu": 0.0494, "memory": 0.10516135953366756, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749481435708, "cpu": 0.0237, "memory": 0.10570744052529335, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749481437710, "cpu": 0.1571, "memory": 0.1062858384102583, "temperature": 37, "efficiency": 64}, {"timestamp": 1749481439710, "cpu": 0.0208, "memory": 0.104072829708457, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749481441712, "cpu": 0.0225, "memory": 0.10460256598889828, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749481443712, "cpu": 0.055900000000000005, "memory": 0.1063456293195486, "temperature": 37, "efficiency": 64}, {"timestamp": 1749481445714, "cpu": 0.0102, "memory": 0.1029076986014843, "temperature": 37, "efficiency": 65}, {"timestamp": 1749481447715, "cpu": 0.008, "memory": 0.10368037037551403, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749481449716, "cpu": 0.015300000000000001, "memory": 0.10546715930104256, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749481451717, "cpu": 0.0203, "memory": 0.10595982894301414, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749481453718, "cpu": 0.0167, "memory": 0.10358057916164398, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749481455719, "cpu": 0.014100000000000001, "memory": 0.10417732410132885, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749481457719, "cpu": 0.0109, "memory": 0.10491604916751385, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749481459721, "cpu": 0.0207, "memory": 0.10679955594241619, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749481461721, "cpu": 0.0386, "memory": 0.10732272639870644, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749481463722, "cpu": 0.0125, "memory": 0.10478976182639599, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749481465724, "cpu": 0.025799999999999997, "memory": 0.1053839921951294, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749481467724, "cpu": 0.0378, "memory": 0.10596797801554203, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749481469727, "cpu": 0.0324, "memory": 0.10771029628813267, "temperature": 37, "efficiency": 64}, {"timestamp": 1749481471728, "cpu": 0.0224, "memory": 0.10431986302137375, "temperature": 37, "efficiency": 65}, {"timestamp": 1749481473729, "cpu": 0.0203, "memory": 0.10611927136778831, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749481475729, "cpu": 0.0202, "memory": 0.10668449103832245, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749481477730, "cpu": 0.0232, "memory": 0.10771751403808594, "temperature": 37, "efficiency": 64}, {"timestamp": 1749481479732, "cpu": 0.0222, "memory": 0.10539265349507332, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749481481733, "cpu": 0.019, "memory": 0.10590935125946999, "temperature": 37, "efficiency": 65}, {"timestamp": 1749481483734, "cpu": 0.016, "memory": 0.10779933072626591, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749481485735, "cpu": 0.0238, "memory": 0.10833926498889923, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749481487736, "cpu": 0.0409, "memory": 0.10893591679632664, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749481489736, "cpu": 0.0283, "memory": 0.10657957755029202, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749481491738, "cpu": 0.023, "memory": 0.10712533257901669, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749481493738, "cpu": 0.0089, "memory": 0.10894914157688618, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749481495740, "cpu": 0.0239, "memory": 0.10955049656331539, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749481497741, "cpu": 0.0268, "memory": 0.10600946843624115, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749481499741, "cpu": 0.024, "memory": 0.10765464976429939, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749481501741, "cpu": 0.009899999999999999, "memory": 0.10889661498367786, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749481503742, "cpu": 0.0089, "memory": 0.10677487589418888, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749481505748, "cpu": 0.0268, "memory": 0.1073190476745367, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749481507751, "cpu": 0.0172, "memory": 0.10795313864946365, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749481509755, "cpu": 0.0174, "memory": 0.1096540130674839, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749481511758, "cpu": 0.019200000000000002, "memory": 0.1101482193917036, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749481513762, "cpu": 0.0077, "memory": 0.10771118104457855, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749481515764, "cpu": 0.0088, "memory": 0.1083124428987503, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749481517766, "cpu": 0.022699999999999998, "memory": 0.10885065421462059, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749481519769, "cpu": 0.010799999999999999, "memory": 0.11061853729188442, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749481521771, "cpu": 0.012899999999999998, "memory": 0.11123493313789368, "temperature": 37, "efficiency": 63.4}, {"timestamp": 1749481523773, "cpu": 0.011000000000000001, "memory": 0.10900218039751053, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749481525775, "cpu": 0.0185, "memory": 0.10984940454363823, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749481527777, "cpu": 0.013999999999999999, "memory": 0.11097704991698265, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749481529779, "cpu": 0.012799999999999999, "memory": 0.10864301584661007, "temperature": 37, "efficiency": 65}, {"timestamp": 1749481531781, "cpu": 0.0279, "memory": 0.10924367234110832, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749481533782, "cpu": 0.012, "memory": 0.1110417302697897, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749481535784, "cpu": 0.0202, "memory": 0.11153770610690117, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749481537785, "cpu": 0.0211, "memory": 0.11219126172363758, "temperature": 37, "efficiency": 64}, {"timestamp": 1749481539786, "cpu": 0.0086, "memory": 0.1098827924579382, "temperature": 37, "efficiency": 65}, {"timestamp": 1749481541788, "cpu": 0.02, "memory": 0.11044442653656006, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749481543788, "cpu": 0.0107, "memory": 0.11219270527362823, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749481545791, "cpu": 0.022000000000000002, "memory": 0.11272761039435863, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749481547791, "cpu": 0.024800000000000003, "memory": 0.10938411578536034, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749481549794, "cpu": 0.0278, "memory": 0.11113290674984455, "temperature": 37, "efficiency": 65}, {"timestamp": 1749481551793, "cpu": 0.0222, "memory": 0.11241389438509941, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749481553795, "cpu": 0.0052, "memory": 0.11031851172447205, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749481555796, "cpu": 0.015300000000000001, "memory": 0.11085211299359798, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749481557798, "cpu": 0.006, "memory": 0.1115226186811924, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749481559798, "cpu": 0.0077, "memory": 0.11331913992762566, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749481561799, "cpu": 0.0084, "memory": 0.11385283432900906, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749481563802, "cpu": 0.016, "memory": 0.11188159696757793, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749481565802, "cpu": 0.0073999999999999995, "memory": 0.11242125183343887, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749481567803, "cpu": 0.023, "memory": 0.11305469088256359, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749481569804, "cpu": 0.0106, "memory": 0.11742468923330307, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749481571805, "cpu": 0.0341, "memory": 0.11799181811511517, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749481573806, "cpu": 0.0038000000000000004, "memory": 0.11428040452301502, "temperature": 37, "efficiency": 65}, {"timestamp": 1749481575807, "cpu": 0.014799999999999999, "memory": 0.11481544934213161, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749481577807, "cpu": 0.0223, "memory": 0.12010717764496803, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749481579808, "cpu": 0.0161, "memory": 0.1217410434037447, "temperature": 37, "efficiency": 64}, {"timestamp": 1749481581807, "cpu": 0.0166, "memory": 0.12234835885465145, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749481583808, "cpu": 0.0891, "memory": 0.1195177435874939, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749481585810, "cpu": 0.067, "memory": 0.12007583864033222, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749481587810, "cpu": 0.0353, "memory": 0.12073931284248829, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749481589811, "cpu": 0.0693, "memory": 0.12237601913511753, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749481591813, "cpu": 0.0357, "memory": 0.11914758943021297, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749481593813, "cpu": 0.0774, "memory": 0.1208749134093523, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749481595815, "cpu": 0.0762, "memory": 0.12136953882873058, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749481597816, "cpu": 0.0368, "memory": 0.12199967168271542, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749481599816, "cpu": 0.0993, "memory": 0.12031807564198971, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749481601818, "cpu": 0.0784, "memory": 0.12096147984266281, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749481603818, "cpu": 0.060700000000000004, "memory": 0.1228399109095335, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749481605817, "cpu": 0.0935, "memory": 0.12343800626695156, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749481607817, "cpu": 0.1948, "memory": 0.1239660196006298, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749481609819, "cpu": 0.0342, "memory": 0.12153028510510921, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749481611819, "cpu": 0.0519, "memory": 0.12208977714180946, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749481615129, "cpu": 0.1564, "memory": 0.12377938255667686, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749481617131, "cpu": 0.5971, "memory": 0.1243014819920063, "temperature": 37, "efficiency": 63.5}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}