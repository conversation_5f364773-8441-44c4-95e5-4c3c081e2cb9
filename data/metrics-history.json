{"timestamp": 1749478937629, "metrics": {"system": {"cpu": {"usage": 0.0146, "temperature": 37, "cores": 10}, "memory": {"used": 18063592, "total": 17179869184, "efficiency": 6.089083461988935}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749478637613}, "performance": {"uptime": 298.292, "efficiency": 64, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 99, "connections": 0}}, "history": [{"timestamp": 1749478737786, "cpu": 0.1503, "memory": 0.08259955793619156, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749478739791, "cpu": 0.0572, "memory": 0.07988987490534782, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749478741786, "cpu": 0.042, "memory": 0.08133421652019024, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749478743787, "cpu": 0.01, "memory": 0.0829658005386591, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749478745788, "cpu": 0.0137, "memory": 0.08020359091460705, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749478747788, "cpu": 0.0103, "memory": 0.08209380321204662, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749478749789, "cpu": 0.0199, "memory": 0.08038277737796307, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749478751790, "cpu": 0.022699999999999998, "memory": 0.08162041194736958, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749478753791, "cpu": 0.09430000000000001, "memory": 0.08324524387717247, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749478755792, "cpu": 0.0953, "memory": 0.08156956173479557, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749478757795, "cpu": 0.1082, "memory": 0.08282423950731754, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749478759795, "cpu": 0.0322, "memory": 0.0852665863931179, "temperature": 37, "efficiency": 64}, {"timestamp": 1749478761796, "cpu": 0.0458, "memory": 0.08291797712445259, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749478763798, "cpu": 0.06319999999999999, "memory": 0.083571532741189, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749478765799, "cpu": 0.0446, "memory": 0.08603287860751152, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749478767799, "cpu": 0.066, "memory": 0.0836674589663744, "temperature": 37, "efficiency": 65}, {"timestamp": 1749478769800, "cpu": 0.0601, "memory": 0.08665206842124462, "temperature": 37, "efficiency": 64}, {"timestamp": 1749478771801, "cpu": 0.19, "memory": 0.08813613094389439, "temperature": 37, "efficiency": 63.4}, {"timestamp": 1749478773801, "cpu": 0.0471, "memory": 0.08673476986587048, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749478775802, "cpu": 0.0492, "memory": 0.08822414092719555, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749478777803, "cpu": 0.2201, "memory": 0.0897520687431097, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749478779826, "cpu": 0.1807, "memory": 0.08911206386983395, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749478781809, "cpu": 0.0407, "memory": 0.08977861143648624, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749478783818, "cpu": 0.08109999999999999, "memory": 0.08678687736392021, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749478785814, "cpu": 0.0205, "memory": 0.08933492936193943, "temperature": 37, "efficiency": 64}, {"timestamp": 1749478787815, "cpu": 0.0862, "memory": 0.08998233824968338, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749478789817, "cpu": 0.0747, "memory": 0.08840793743729591, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749478791820, "cpu": 0.0849, "memory": 0.08990317583084106, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749478793822, "cpu": 0.1305, "memory": 0.09145108051598072, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749478795824, "cpu": 0.061799999999999994, "memory": 0.0901282299309969, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749478797826, "cpu": 0.034, "memory": 0.08823415264487267, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749478799829, "cpu": 0.050600000000000006, "memory": 0.08973013609647751, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749478801830, "cpu": 0.0234, "memory": 0.09152069687843323, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749478803831, "cpu": 0.0134, "memory": 0.0890473835170269, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749478805833, "cpu": 0.0547, "memory": 0.09061787277460098, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749478807833, "cpu": 0.0606, "memory": 0.09219604544341564, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749478809834, "cpu": 0.0333, "memory": 0.09051547385752201, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749478811835, "cpu": 0.0883, "memory": 0.09109699167311192, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749478813836, "cpu": 0.010799999999999999, "memory": 0.0927455723285675, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749478815836, "cpu": 0.0109, "memory": 0.09122053161263466, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749478817838, "cpu": 0.027700000000000002, "memory": 0.09188842959702015, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749478819839, "cpu": 0.0106, "memory": 0.09438111446797848, "temperature": 37, "efficiency": 63.3}, {"timestamp": 1749478821840, "cpu": 0.0113, "memory": 0.09182007052004337, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749478823842, "cpu": 0.032, "memory": 0.09256009943783283, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749478825842, "cpu": 0.0164, "memory": 0.09134397841989994, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749478827843, "cpu": 0.012899999999999998, "memory": 0.09332448244094849, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749478829844, "cpu": 0.06939999999999999, "memory": 0.09499024599790573, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749478831845, "cpu": 0.0157, "memory": 0.09216298349201679, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749478833846, "cpu": 0.018000000000000002, "memory": 0.09373864158987999, "temperature": 37, "efficiency": 64}, {"timestamp": 1749478835847, "cpu": 0.0395, "memory": 0.09523681364953518, "temperature": 37, "efficiency": 64}, {"timestamp": 1749478837848, "cpu": 0.0172, "memory": 0.09261853992938995, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749478839849, "cpu": 0.0138, "memory": 0.09516389109194279, "temperature": 37, "efficiency": 64}, {"timestamp": 1749478841850, "cpu": 0.3301, "memory": 0.0956560019403696, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749478843851, "cpu": 0.022000000000000002, "memory": 0.09333966299891472, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749478845852, "cpu": 0.0093, "memory": 0.09613046422600746, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749478847853, "cpu": 0.026400000000000003, "memory": 0.0972650945186615, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749478849856, "cpu": 0.0372, "memory": 0.09565800428390503, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749478851854, "cpu": 0.020900000000000002, "memory": 0.09722704999148846, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749478853856, "cpu": 0.0679, "memory": 0.09779720567166805, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749478855857, "cpu": 0.027, "memory": 0.09586573578417301, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749478857858, "cpu": 0.0176, "memory": 0.09779920801520348, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749478859859, "cpu": 0.0276, "memory": 0.09504794143140316, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749478861860, "cpu": 0.0408, "memory": 0.09668376296758652, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749478863862, "cpu": 0.1418, "memory": 0.0982996542006731, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749478865862, "cpu": 0.0845, "memory": 0.09577502496540546, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749478867863, "cpu": 0.5898, "memory": 0.09740064851939678, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749478869864, "cpu": 0.0278, "memory": 0.09606587700545788, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749478871870, "cpu": 0.7964000000000001, "memory": 0.09662695229053497, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749478873867, "cpu": 0.3184, "memory": 0.0985593069344759, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749478875868, "cpu": 0.015300000000000001, "memory": 0.0971425324678421, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749478877870, "cpu": 0.7398, "memory": 0.09777862578630447, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749478879870, "cpu": 0.021900000000000003, "memory": 0.10038437321782112, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749478881872, "cpu": 0.0114, "memory": 0.09721415117383003, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749478883872, "cpu": 0.1975, "memory": 0.09796405211091042, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749478885874, "cpu": 0.0098, "memory": 0.10050404816865921, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749478887876, "cpu": 0.0611, "memory": 0.09796875528991222, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749478889877, "cpu": 0.388, "memory": 0.09966744109988213, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749478891878, "cpu": 0.0146, "memory": 0.10116826742887497, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749478893879, "cpu": 0.2265, "memory": 0.09859479032456875, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749478895880, "cpu": 0.3981, "memory": 0.1000555232167244, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749478897881, "cpu": 0.8002, "memory": 0.10187234729528427, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749478899883, "cpu": 0.0838, "memory": 0.10019089095294476, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749478901882, "cpu": 0.3717, "memory": 0.10068537667393684, "temperature": 37, "efficiency": 66}, {"timestamp": 1749478903884, "cpu": 0.2691, "memory": 0.1022679265588522, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749478905885, "cpu": 0.0179, "memory": 0.10063429363071918, "temperature": 37, "efficiency": 65}, {"timestamp": 1749478907890, "cpu": 0.3033, "memory": 0.10128193534910679, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749478909893, "cpu": 0.05330000000000001, "memory": 0.09962399490177631, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749478911895, "cpu": 0.0352, "memory": 0.10112184099853039, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749478913897, "cpu": 0.0834, "memory": 0.10168608278036118, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749478915898, "cpu": 0.0361, "memory": 0.10019252076745033, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749478917898, "cpu": 0.021, "memory": 0.101811857894063, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749478919899, "cpu": 0.1305, "memory": 0.10324809700250626, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749478921899, "cpu": 0.019200000000000002, "memory": 0.10093185119330883, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749478923901, "cpu": 0.2342, "memory": 0.10272585786879063, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749478925902, "cpu": 0.25880000000000003, "memory": 0.10446598753333092, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749478927902, "cpu": 0.0503, "memory": 0.10197083465754986, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749478929903, "cpu": 0.0211, "memory": 0.1044328324496746, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749478931905, "cpu": 0.5336, "memory": 0.1052801962941885, "temperature": 37, "efficiency": 63.4}, {"timestamp": 1749478933905, "cpu": 0.611, "memory": 0.10274653322994709, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749478935905, "cpu": 0.0146, "memory": 0.10514394380152225, "temperature": 37, "efficiency": 64}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}