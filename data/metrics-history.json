{"timestamp": 1749483461825, "metrics": {"system": {"cpu": {"usage": 0.0134, "temperature": 37, "cores": 10}, "memory": {"used": 19404640, "total": 17179869184, "efficiency": 18.8790935359589}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749482861823}, "performance": {"uptime": 598.618, "efficiency": 68.3, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749483262161, "cpu": 0.0067, "memory": 0.11803768575191498, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749483264163, "cpu": 0.018699999999999998, "memory": 0.11850777082145214, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749483266163, "cpu": 0.0145, "memory": 0.12118024751543999, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749483268164, "cpu": 0.0206, "memory": 0.11880742385983467, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749483270165, "cpu": 0.0172, "memory": 0.11930912733078003, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749483272166, "cpu": 0.0154, "memory": 0.12116753496229649, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749483274167, "cpu": 0.0206, "memory": 0.12167096138000488, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749483276168, "cpu": 0.0106, "memory": 0.1181082334369421, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749483278169, "cpu": 0.0186, "memory": 0.1197014469653368, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749483280170, "cpu": 0.0068, "memory": 0.12020324356853962, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749483282170, "cpu": 0.0203, "memory": 0.12187408283352852, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749483284172, "cpu": 0.0151, "memory": 0.12244395911693573, "temperature": 37, "efficiency": 64}, {"timestamp": 1749483286173, "cpu": 0.010799999999999999, "memory": 0.1188377384096384, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749483288174, "cpu": 0.012400000000000001, "memory": 0.12035612016916275, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749483290175, "cpu": 0.0215, "memory": 0.12087137438356876, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749483292333, "cpu": 0.0145, "memory": 0.12246635742485523, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749483294334, "cpu": 0.10020000000000001, "memory": 0.12293606996536255, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749483296335, "cpu": 0.0098, "memory": 0.11951695196330547, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749483298336, "cpu": 0.1208, "memory": 0.12125573121011257, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749483300337, "cpu": 0.029799999999999997, "memory": 0.12177866883575916, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749483302338, "cpu": 0.0164, "memory": 0.12336173094809055, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749483304339, "cpu": 0.0166, "memory": 0.11977097019553185, "temperature": 37, "efficiency": 65}, {"timestamp": 1749483306339, "cpu": 0.07919999999999999, "memory": 0.12026769109070301, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749483308342, "cpu": 0.0178, "memory": 0.1219040248543024, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749483310343, "cpu": 0.0083, "memory": 0.12271585874259472, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749483312344, "cpu": 0.028499999999999998, "memory": 0.12057730928063393, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749483314345, "cpu": 0.0186, "memory": 0.12114662677049637, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749483316346, "cpu": 0.0213, "memory": 0.12167864479124546, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749483318348, "cpu": 0.0332, "memory": 0.12320727109909058, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749483320349, "cpu": 0.012, "memory": 0.12379265390336514, "temperature": 37, "efficiency": 64}, {"timestamp": 1749483322350, "cpu": 0.0068, "memory": 0.12154378928244114, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749483324351, "cpu": 0.0332, "memory": 0.12208577245473862, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749483326352, "cpu": 0.0098, "memory": 0.12246961705386639, "temperature": 37, "efficiency": 66}, {"timestamp": 1749483328352, "cpu": 0.7053, "memory": 0.12418758124113083, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749483330353, "cpu": 0.22160000000000002, "memory": 0.12226318940520287, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749483332354, "cpu": 0.0604, "memory": 0.1257806085050106, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749483334356, "cpu": 0.22669999999999998, "memory": 0.12275632470846176, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749483336356, "cpu": 0.12459999999999999, "memory": 0.12529678642749786, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749483338356, "cpu": 0.0114, "memory": 0.1242173369973898, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749483340359, "cpu": 0.3828, "memory": 0.12490097433328629, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749483342360, "cpu": 0.0531, "memory": 0.1242486760020256, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749483344362, "cpu": 0.012899999999999998, "memory": 0.1257745549082756, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749483346362, "cpu": 0.3862, "memory": 0.12728837318718433, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749483348363, "cpu": 0.0291, "memory": 0.12596054002642632, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749483350366, "cpu": 0.22330000000000003, "memory": 0.127487163990736, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749483352366, "cpu": 0.1769, "memory": 0.12611672282218933, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749483354367, "cpu": 0.0686, "memory": 0.12753857299685478, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749483356368, "cpu": 0.0114, "memory": 0.12591364793479443, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749483358369, "cpu": 0.1949, "memory": 0.12750057503581047, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749483360369, "cpu": 0.3761, "memory": 0.12905527837574482, "temperature": 37, "efficiency": 65}, {"timestamp": 1749483362371, "cpu": 0.0125, "memory": 0.1283386256545782, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749483364371, "cpu": 0.3062, "memory": 0.12884577736258507, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749483366372, "cpu": 0.08589999999999999, "memory": 0.12685018591582775, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749483368372, "cpu": 0.0193, "memory": 0.1294469926506281, "temperature": 37, "efficiency": 64}, {"timestamp": 1749483370373, "cpu": 0.3648, "memory": 0.1300083938986063, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749483372373, "cpu": 0.044000000000000004, "memory": 0.12974319979548454, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749483374374, "cpu": 0.0218, "memory": 0.13138176873326302, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749483376373, "cpu": 0.2919, "memory": 0.12897420674562454, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749483378374, "cpu": 0.0546, "memory": 0.09915819391608238, "temperature": 37, "efficiency": 71.6}, {"timestamp": 1749483380376, "cpu": 0.2756, "memory": 0.10090530849993229, "temperature": 37, "efficiency": 71.1}, {"timestamp": 1749483382379, "cpu": 0.12359999999999999, "memory": 0.09946748614311218, "temperature": 37, "efficiency": 71.5}, {"timestamp": 1749483384380, "cpu": 0.046700000000000005, "memory": 0.10098838247358799, "temperature": 37, "efficiency": 71.1}, {"timestamp": 1749483386382, "cpu": 0.0131, "memory": 0.10137376375496387, "temperature": 37, "efficiency": 71.1}, {"timestamp": 1749483388385, "cpu": 0.6217, "memory": 0.10303668677806854, "temperature": 37, "efficiency": 70.5}, {"timestamp": 1749483390386, "cpu": 0.3657, "memory": 0.10436153970658779, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749483392389, "cpu": 0.0281, "memory": 0.10300097055733204, "temperature": 37, "efficiency": 71.7}, {"timestamp": 1749483394390, "cpu": 0.2751, "memory": 0.10394463315606117, "temperature": 37, "efficiency": 71.4}, {"timestamp": 1749483396391, "cpu": 0.043800000000000006, "memory": 0.10249046608805656, "temperature": 37, "efficiency": 70.8}, {"timestamp": 1749483398393, "cpu": 0.0319, "memory": 0.10528438724577427, "temperature": 37, "efficiency": 70.1}, {"timestamp": 1749483400396, "cpu": 0.5454, "memory": 0.10167071595788002, "temperature": 37, "efficiency": 70.8}, {"timestamp": 1749483402398, "cpu": 0.1503, "memory": 0.10513002052903175, "temperature": 37, "efficiency": 70.1}, {"timestamp": 1749483404396, "cpu": 0.0172, "memory": 0.10258625261485577, "temperature": 37, "efficiency": 70.8}, {"timestamp": 1749483406396, "cpu": 0.48729999999999996, "memory": 0.10411874391138554, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749483408400, "cpu": 0.0704, "memory": 0.10302639566361904, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749483410401, "cpu": 0.3321, "memory": 0.10480298660695553, "temperature": 37, "efficiency": 70.1}, {"timestamp": 1749483412404, "cpu": 0.40249999999999997, "memory": 0.10729143396019936, "temperature": 37, "efficiency": 69.5}, {"timestamp": 1749483414405, "cpu": 0.018000000000000002, "memory": 0.10468810796737671, "temperature": 37, "efficiency": 70.3}, {"timestamp": 1749483416407, "cpu": 0.0239, "memory": 0.10741539299488068, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749483418410, "cpu": 0.2111, "memory": 0.10517504997551441, "temperature": 37, "efficiency": 70.1}, {"timestamp": 1749483420408, "cpu": 0.31930000000000003, "memory": 0.10660267435014248, "temperature": 37, "efficiency": 69.7}, {"timestamp": 1749483422410, "cpu": 0.1349, "memory": 0.1062038354575634, "temperature": 37, "efficiency": 70.9}, {"timestamp": 1749483424411, "cpu": 0.20249999999999999, "memory": 0.10733767412602901, "temperature": 37, "efficiency": 70.6}, {"timestamp": 1749483426413, "cpu": 0.0101, "memory": 0.10563642717897892, "temperature": 37, "efficiency": 70}, {"timestamp": 1749483428415, "cpu": 0.0162, "memory": 0.10807388462126255, "temperature": 37, "efficiency": 69.5}, {"timestamp": 1749483430417, "cpu": 0.4874, "memory": 0.10870867408812046, "temperature": 37, "efficiency": 69.1}, {"timestamp": 1749483432419, "cpu": 0.0104, "memory": 0.10834559798240662, "temperature": 37, "efficiency": 69.4}, {"timestamp": 1749483434419, "cpu": 0.0279, "memory": 0.10987701825797558, "temperature": 37, "efficiency": 69}, {"timestamp": 1749483436423, "cpu": 0.1817, "memory": 0.10738540440797806, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749483438422, "cpu": 0.0115, "memory": 0.10984768159687519, "temperature": 37, "efficiency": 69}, {"timestamp": 1749483440424, "cpu": 0.2932, "memory": 0.10705213062465191, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749483442426, "cpu": 0.3472, "memory": 0.10993955656886101, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749483444427, "cpu": 0.0154, "memory": 0.10774084366858006, "temperature": 37, "efficiency": 69.5}, {"timestamp": 1749483446430, "cpu": 0.0185, "memory": 0.11172061786055565, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749483448431, "cpu": 0.2395, "memory": 0.10942630469799042, "temperature": 37, "efficiency": 69.1}, {"timestamp": 1749483450433, "cpu": 0.185, "memory": 0.11076703667640686, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749483452435, "cpu": 0.0229, "memory": 0.10968153364956379, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749483454436, "cpu": 0.012799999999999999, "memory": 0.11101593263447285, "temperature": 37, "efficiency": 69.9}, {"timestamp": 1749483456438, "cpu": 0.0197, "memory": 0.11237971484661102, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749483458441, "cpu": 0.0114, "memory": 0.1107565127313137, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749483460441, "cpu": 0.0134, "memory": 0.11294987052679062, "temperature": 37, "efficiency": 68.3}], "alerts": [], "optimizations": [{"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}