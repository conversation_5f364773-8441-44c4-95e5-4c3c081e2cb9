{"timestamp": 1749486772809, "metrics": {"system": {"cpu": {"usage": 0.0109, "temperature": 37, "cores": 10}, "memory": {"used": 17289312, "total": 17179869184, "efficiency": 19.874270358769934}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749486472816}, "performance": {"uptime": 298.219, "efficiency": 68.6, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 99, "connections": 0}}, "history": [{"timestamp": 1749486572960, "cpu": 0.6767, "memory": 0.13046059757471085, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749486574961, "cpu": 0.016, "memory": 0.130171375349164, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749486576962, "cpu": 0.0083, "memory": 0.13013826683163643, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749486578963, "cpu": 0.0095, "memory": 0.13151010498404503, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749486580964, "cpu": 0.0088, "memory": 0.1342153176665306, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749486582963, "cpu": 1.183, "memory": 0.09535439312458038, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749486584965, "cpu": 0.0139, "memory": 0.09824372828006744, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749486586966, "cpu": 0.013999999999999999, "memory": 0.10482002981007099, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749486588966, "cpu": 0.013, "memory": 0.09567863307893276, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749486590967, "cpu": 0.0144, "memory": 0.09547672234475613, "temperature": 37, "efficiency": 68}, {"timestamp": 1749486592967, "cpu": 0.6906, "memory": 0.09641656652092934, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749486594968, "cpu": 0.0237, "memory": 0.0969892367720604, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749486596970, "cpu": 0.0161, "memory": 0.09603388607501984, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749486598969, "cpu": 0.0213, "memory": 0.09677023626863956, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749486600970, "cpu": 0.0073, "memory": 0.09731035679578781, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749486602971, "cpu": 0.713, "memory": 0.09800442494452, "temperature": 37, "efficiency": 67}, {"timestamp": 1749486604972, "cpu": 0.0156, "memory": 0.10079732164740562, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749486606972, "cpu": 0.009000000000000001, "memory": 0.1072318758815527, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749486608973, "cpu": 0.0116, "memory": 0.09827413596212864, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749486610971, "cpu": 0.012, "memory": 0.09810230694711208, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749486612970, "cpu": 0.7113999999999999, "memory": 0.0992440152913332, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749486614971, "cpu": 0.014899999999999998, "memory": 0.09882333688437939, "temperature": 37, "efficiency": 67}, {"timestamp": 1749486616970, "cpu": 0.0131, "memory": 0.09845439344644547, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749486618970, "cpu": 0.009899999999999999, "memory": 0.10005924850702286, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749486620973, "cpu": 0.0116, "memory": 0.09956769645214081, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749486622969, "cpu": 0.6639, "memory": 0.10037431493401527, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749486624969, "cpu": 0.0196, "memory": 0.10094577446579933, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749486626970, "cpu": 0.016, "memory": 0.09979973547160625, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749486628970, "cpu": 0.0142, "memory": 0.10048206895589828, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749486630971, "cpu": 0.012, "memory": 0.10114694014191628, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749486632971, "cpu": 0.6672, "memory": 0.1021727453917265, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749486634971, "cpu": 0.011000000000000001, "memory": 0.10496661998331547, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749486636972, "cpu": 0.009399999999999999, "memory": 0.11110310442745686, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749486638972, "cpu": 0.0113, "memory": 0.10883812792599201, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749486640973, "cpu": 0.0112, "memory": 0.10847928933799267, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749486642973, "cpu": 0.6605, "memory": 0.10947296395897865, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749486644975, "cpu": 0.0235, "memory": 0.10897400788962841, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749486646975, "cpu": 0.0084, "memory": 0.10873368009924889, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749486648975, "cpu": 0.0161, "memory": 0.11064782738685608, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749486650977, "cpu": 0.018799999999999997, "memory": 0.11337790638208389, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749486652976, "cpu": 0.6721, "memory": 0.11605210602283478, "temperature": 37, "efficiency": 65}, {"timestamp": 1749486654977, "cpu": 0.0101, "memory": 0.11668913066387177, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749486656979, "cpu": 0.0155, "memory": 0.11632055975496769, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749486658979, "cpu": 0.012400000000000001, "memory": 0.1170736737549305, "temperature": 37, "efficiency": 65}, {"timestamp": 1749486660979, "cpu": 0.0136, "memory": 0.11755540035665035, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749486662980, "cpu": 1.2784, "memory": 0.11824183166027069, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749486664980, "cpu": 0.0073999999999999995, "memory": 0.11777430772781372, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749486666981, "cpu": 0.010799999999999999, "memory": 0.11744624935090542, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749486668982, "cpu": 0.008, "memory": 0.1194290816783905, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749486670982, "cpu": 0.0422, "memory": 0.1250735018402338, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749486672983, "cpu": 0.634, "memory": 0.1248569693416357, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749486674984, "cpu": 0.012799999999999999, "memory": 0.12541897594928741, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749486676984, "cpu": 0.006200000000000001, "memory": 0.12526744976639748, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749486678986, "cpu": 0.0126, "memory": 0.12715961784124374, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749486680986, "cpu": 0.0083, "memory": 0.12968899682164192, "temperature": 37, "efficiency": 63.3}, {"timestamp": 1749486682987, "cpu": 0.7126, "memory": 0.13295095413923264, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749486684989, "cpu": 0.031100000000000003, "memory": 0.13383403420448303, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749486686990, "cpu": 0.018000000000000002, "memory": 0.13299058191478252, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749486688991, "cpu": 0.0088, "memory": 0.13372628018260002, "temperature": 37, "efficiency": 64}, {"timestamp": 1749486690991, "cpu": 0.01, "memory": 0.13420246541500092, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749486692995, "cpu": 0.8992, "memory": 0.13494249433279037, "temperature": 37, "efficiency": 63.4}, {"timestamp": 1749486694995, "cpu": 0.031, "memory": 0.1345597207546234, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749486696995, "cpu": 0.0136, "memory": 0.1346935983747244, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749486698997, "cpu": 0.0138, "memory": 0.13577500358223915, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749486700999, "cpu": 0.0144, "memory": 0.13524498790502548, "temperature": 37, "efficiency": 64}, {"timestamp": 1749486702999, "cpu": 0.6344, "memory": 0.13644229620695114, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749486705001, "cpu": 0.0115, "memory": 0.13603460974991322, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749486707001, "cpu": 0.0069, "memory": 0.13558650389313698, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749486709001, "cpu": 0.0071, "memory": 0.13737059198319912, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749486711003, "cpu": 0.0155, "memory": 0.13681543059647083, "temperature": 37, "efficiency": 64}, {"timestamp": 1749486713004, "cpu": 0.7095, "memory": 0.13746009208261967, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749486715006, "cpu": 0.0189, "memory": 0.1380138099193573, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749486717007, "cpu": 0.0143, "memory": 0.13691936619579792, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749486719008, "cpu": 0.0182, "memory": 0.13760710135102272, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749486721009, "cpu": 0.016, "memory": 0.13814037665724754, "temperature": 37, "efficiency": 64}, {"timestamp": 1749486723010, "cpu": 0.6287, "memory": 0.1388424076139927, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749486725010, "cpu": 0.021900000000000003, "memory": 0.13837548904120922, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749486727012, "cpu": 0.0228, "memory": 0.13830824755132198, "temperature": 37, "efficiency": 64}, {"timestamp": 1749486729013, "cpu": 0.0093, "memory": 0.1396981067955494, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749486731015, "cpu": 0.0215, "memory": 0.13918126933276653, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749486733016, "cpu": 1.229, "memory": 0.14024903066456318, "temperature": 37, "efficiency": 63.1}, {"timestamp": 1749486735017, "cpu": 0.0214, "memory": 0.13982574455440044, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749486737017, "cpu": 0.012400000000000001, "memory": 0.09522992186248302, "temperature": 37, "efficiency": 69.7}, {"timestamp": 1749486739018, "cpu": 0.01, "memory": 0.09723883122205734, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749486741019, "cpu": 0.010799999999999999, "memory": 0.09701484814286232, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749486743020, "cpu": 1.1776, "memory": 0.09793187491595745, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749486745021, "cpu": 0.012199999999999999, "memory": 0.09845192544162273, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749486747023, "cpu": 0.0273, "memory": 0.09754435159265995, "temperature": 37, "efficiency": 69.1}, {"timestamp": 1749486749023, "cpu": 0.012799999999999999, "memory": 0.09807590395212173, "temperature": 37, "efficiency": 69}, {"timestamp": 1749486751024, "cpu": 0.009600000000000001, "memory": 0.09859376586973667, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749486753025, "cpu": 1.3263, "memory": 0.09926725178956985, "temperature": 37, "efficiency": 68.2}, {"timestamp": 1749486755026, "cpu": 0.0166, "memory": 0.09878566488623619, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749486757027, "cpu": 0.0168, "memory": 0.0986877828836441, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749486759049, "cpu": 0.0571, "memory": 0.10024234652519226, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749486761030, "cpu": 0.0214, "memory": 0.09959866292774677, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749486763031, "cpu": 1.1875, "memory": 0.10046521201729774, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749486765032, "cpu": 0.0222, "memory": 0.10031885467469692, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749486767033, "cpu": 0.0619, "memory": 0.11031688190996647, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749486769034, "cpu": 0.0144, "memory": 0.1013473141938448, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749486771035, "cpu": 0.0109, "memory": 0.1006370410323143, "temperature": 37, "efficiency": 68.6}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}