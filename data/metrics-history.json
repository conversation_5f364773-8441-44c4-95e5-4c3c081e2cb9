{"timestamp": 1749484811171, "metrics": {"system": {"cpu": {"usage": 0.0186, "temperature": 37, "cores": 10}, "memory": {"used": 17489864, "total": 17179869184, "efficiency": 20.86756248841735}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749484511166}, "performance": {"uptime": 298.282, "efficiency": 68.9, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 99, "connections": 0}}, "history": [{"timestamp": 1749484611345, "cpu": 0.0186, "memory": 0.09779734537005424, "temperature": 37, "efficiency": 68}, {"timestamp": 1749484613345, "cpu": 0.0279, "memory": 0.10431171394884586, "temperature": 37, "efficiency": 67}, {"timestamp": 1749484615345, "cpu": 0.6679, "memory": 0.10494864545762539, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749484617347, "cpu": 0.0234, "memory": 0.10334993712604046, "temperature": 37, "efficiency": 68.5}, {"timestamp": 1749484619347, "cpu": 0.0323, "memory": 0.10186517611145973, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749484621348, "cpu": 0.40740000000000004, "memory": 0.10225027799606323, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749484623350, "cpu": 0.0499, "memory": 0.10889736004173756, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749484625358, "cpu": 1.1569, "memory": 0.10824170894920826, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749484627353, "cpu": 1.0124, "memory": 0.10726382024586201, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749484629352, "cpu": 0.021599999999999998, "memory": 0.10639163665473461, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749484631352, "cpu": 0.1043, "memory": 0.11192038655281067, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749484633353, "cpu": 0.8375, "memory": 0.11376412585377693, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749484635371, "cpu": 1.2603, "memory": 0.11600065045058727, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749484637358, "cpu": 0.20339999999999997, "memory": 0.11460208334028721, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749484639358, "cpu": 0.18660000000000002, "memory": 0.1151693519204855, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749484641361, "cpu": 0.0481, "memory": 0.12012403458356857, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749484643363, "cpu": 0.0629, "memory": 0.12053102254867554, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749484645362, "cpu": 1.3026, "memory": 0.11760774068534374, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749484647362, "cpu": 0.05550000000000001, "memory": 0.11974917724728584, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749484649365, "cpu": 0.3461, "memory": 0.11846581473946571, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749484651366, "cpu": 0.416, "memory": 0.12499736621975899, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749484653366, "cpu": 0.0817, "memory": 0.12519643642008305, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749484655367, "cpu": 0.4894, "memory": 0.1272556371986866, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749484657368, "cpu": 0.6164000000000001, "memory": 0.1263241283595562, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749484659370, "cpu": 0.3442, "memory": 0.12551876716315746, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749484661369, "cpu": 0.1449, "memory": 0.12826421298086643, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749484663371, "cpu": 1.0934, "memory": 0.1299642026424408, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749484665372, "cpu": 0.6797, "memory": 0.13199113309383392, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749484667373, "cpu": 0.062200000000000005, "memory": 0.129746925085783, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749484669374, "cpu": 0.716, "memory": 0.13044355437159538, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749484671376, "cpu": 0.0616, "memory": 0.13573500327765942, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749484673377, "cpu": 0.169, "memory": 0.13580764643847942, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749484675378, "cpu": 0.9489, "memory": 0.13636904768645763, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749484677380, "cpu": 0.246, "memory": 0.1342057716101408, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749484679380, "cpu": 0.15380000000000002, "memory": 0.14069112949073315, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749484681381, "cpu": 0.6975, "memory": 0.14001321978867054, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749484683383, "cpu": 0.0754, "memory": 0.13681184500455856, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749484685383, "cpu": 0.4823, "memory": 0.13538654893636703, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749484687383, "cpu": 1.3511, "memory": 0.13600802049040794, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749484689383, "cpu": 0.068, "memory": 0.1425900962203741, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749484691385, "cpu": 0.1936, "memory": 0.1399169210344553, "temperature": 37, "efficiency": 66}, {"timestamp": 1749484693387, "cpu": 0.45459999999999995, "memory": 0.09562047198414803, "temperature": 37, "efficiency": 68.2}, {"timestamp": 1749484695388, "cpu": 1.4506, "memory": 0.09498177096247673, "temperature": 37, "efficiency": 68}, {"timestamp": 1749484697388, "cpu": 0.0616, "memory": 0.10180524550378323, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749484699389, "cpu": 0.6671, "memory": 0.10243123397231102, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749484701390, "cpu": 0.31020000000000003, "memory": 0.10201823897659779, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749484703391, "cpu": 0.5412, "memory": 0.1019652932882309, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749484705392, "cpu": 0.8298, "memory": 0.10449029505252838, "temperature": 37, "efficiency": 67}, {"timestamp": 1749484707393, "cpu": 0.0456, "memory": 0.10992488823831081, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749484709394, "cpu": 0.06359999999999999, "memory": 0.10903533548116684, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749484711399, "cpu": 1.4946, "memory": 0.10857800953090191, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749484713399, "cpu": 0.4131, "memory": 0.1081276684999466, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749484715399, "cpu": 1.7548000000000001, "memory": 0.106858741492033, "temperature": 37, "efficiency": 68}, {"timestamp": 1749484717409, "cpu": 1.1733, "memory": 0.10740701109170914, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749484719403, "cpu": 0.2407, "memory": 0.11406107805669308, "temperature": 37, "efficiency": 66}, {"timestamp": 1749484721549, "cpu": 0.7743, "memory": 0.11156587861478329, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749484723410, "cpu": 1.0409, "memory": 0.1135305967181921, "temperature": 37, "efficiency": 66.3}, {"timestamp": 1749484725410, "cpu": 0.4588, "memory": 0.11224080808460712, "temperature": 37, "efficiency": 68}, {"timestamp": 1749484727410, "cpu": 0.0841, "memory": 0.11879922822117805, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749484729412, "cpu": 1.4876, "memory": 0.11947192251682281, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749484731413, "cpu": 0.1529, "memory": 0.11900071986019611, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749484733414, "cpu": 0.1745, "memory": 0.11912784539163113, "temperature": 37, "efficiency": 66.5}, {"timestamp": 1749484735415, "cpu": 1.9342000000000001, "memory": 0.11973395012319088, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749484737415, "cpu": 0.0314, "memory": 0.12646145187318325, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749484739417, "cpu": 0.0717, "memory": 0.1284523867070675, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749484741418, "cpu": 1.0333999999999999, "memory": 0.12813652865588665, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749484743420, "cpu": 0.0983, "memory": 0.12761522084474564, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749484745419, "cpu": 0.7178, "memory": 0.12661856599152088, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749484747421, "cpu": 0.5348, "memory": 0.12719868682324886, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749484749423, "cpu": 0.0588, "memory": 0.1336569432169199, "temperature": 37, "efficiency": 65}, {"timestamp": 1749484751422, "cpu": 0.6116, "memory": 0.13108151033520699, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749484753423, "cpu": 0.7394999999999999, "memory": 0.13314969837665558, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749484755425, "cpu": 0.9367000000000001, "memory": 0.1317768357694149, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749484757426, "cpu": 0.0558, "memory": 0.1381636131554842, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749484759426, "cpu": 1.0697999999999999, "memory": 0.13870340771973133, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749484761426, "cpu": 0.47869999999999996, "memory": 0.13832543045282364, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749484763428, "cpu": 0.0758, "memory": 0.13834196142852306, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749484765429, "cpu": 0.7944, "memory": 0.1409472431987524, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749484767431, "cpu": 0.0229, "memory": 0.13929535634815693, "temperature": 37, "efficiency": 65}, {"timestamp": 1749484769431, "cpu": 0.042499999999999996, "memory": 0.14571892097592354, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749484771432, "cpu": 1.2179, "memory": 0.0870338175445795, "temperature": 37, "efficiency": 71.8}, {"timestamp": 1749484773433, "cpu": 0.055999999999999994, "memory": 0.0876192469149828, "temperature": 37, "efficiency": 70.9}, {"timestamp": 1749484775434, "cpu": 0.5584, "memory": 0.09421324357390404, "temperature": 37, "efficiency": 69.5}, {"timestamp": 1749484777435, "cpu": 0.5201, "memory": 0.09472309611737728, "temperature": 37, "efficiency": 69.4}, {"timestamp": 1749484779436, "cpu": 0.0121, "memory": 0.09435811080038548, "temperature": 37, "efficiency": 69.7}, {"timestamp": 1749484781437, "cpu": 0.3728, "memory": 0.09821918793022633, "temperature": 37, "efficiency": 70.4}, {"timestamp": 1749484783437, "cpu": 0.6526, "memory": 0.09267679415643215, "temperature": 37, "efficiency": 69.9}, {"timestamp": 1749484785439, "cpu": 0.6141, "memory": 0.09970646351575851, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749484787441, "cpu": 0.0395, "memory": 0.09411140345036983, "temperature": 37, "efficiency": 69.7}, {"timestamp": 1749484789443, "cpu": 0.036699999999999997, "memory": 0.09311838075518608, "temperature": 37, "efficiency": 70}, {"timestamp": 1749484791444, "cpu": 0.3159, "memory": 0.09970990940928459, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749484793445, "cpu": 0.12669999999999998, "memory": 0.0998473260551691, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749484795443, "cpu": 0.2593, "memory": 0.09673391468822956, "temperature": 37, "efficiency": 70.8}, {"timestamp": 1749484797444, "cpu": 0.030899999999999997, "memory": 0.098867854103446, "temperature": 37, "efficiency": 70.3}, {"timestamp": 1749484799445, "cpu": 0.0494, "memory": 0.09743701666593552, "temperature": 37, "efficiency": 69.5}, {"timestamp": 1749484801447, "cpu": 1.6105999999999998, "memory": 0.10392912663519382, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749484803448, "cpu": 0.13470000000000001, "memory": 0.10164990089833736, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749484805447, "cpu": 0.06899999999999999, "memory": 0.1010681502521038, "temperature": 37, "efficiency": 70.3}, {"timestamp": 1749484807448, "cpu": 0.0366, "memory": 0.10846229270100594, "temperature": 37, "efficiency": 69.1}, {"timestamp": 1749484809448, "cpu": 0.0186, "memory": 0.10180440731346607, "temperature": 37, "efficiency": 68.9}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}