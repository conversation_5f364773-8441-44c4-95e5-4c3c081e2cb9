{"timestamp": 1749483761826, "metrics": {"system": {"cpu": {"usage": 0.0182, "temperature": 37, "cores": 10}, "memory": {"used": 18459024, "total": 17179869184, "efficiency": 30.453739872685176}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749482861823}, "performance": {"uptime": 898.798, "efficiency": 72.1, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749483562517, "cpu": 0.013, "memory": 0.12379423715174198, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749483564518, "cpu": 0.006200000000000001, "memory": 0.12432835064828396, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749483566519, "cpu": 0.0057, "memory": 0.12419680133461952, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749483568519, "cpu": 0.009000000000000001, "memory": 0.1266141887754202, "temperature": 37, "efficiency": 65}, {"timestamp": 1749483570521, "cpu": 0.0059, "memory": 0.12403847649693489, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749483572522, "cpu": 0.006, "memory": 0.1269374042749405, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749483574523, "cpu": 0.006200000000000001, "memory": 0.12430353090167046, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749483576523, "cpu": 0.0083, "memory": 0.12565762735903263, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749483578525, "cpu": 0.0184, "memory": 0.12803911231458187, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749483580526, "cpu": 0.012899999999999998, "memory": 0.12627169489860535, "temperature": 37, "efficiency": 66.4}, {"timestamp": 1749483582527, "cpu": 0.0063999999999999994, "memory": 0.1278483308851719, "temperature": 37, "efficiency": 66}, {"timestamp": 1749483584528, "cpu": 0.0111, "memory": 0.12518512085080147, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749483586529, "cpu": 0.0063, "memory": 0.12760600075125694, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749483588529, "cpu": 0.006200000000000001, "memory": 0.12918617576360703, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749483590531, "cpu": 0.006200000000000001, "memory": 0.12768958695232868, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749483592531, "cpu": 0.0111, "memory": 0.13005998916924, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749483594532, "cpu": 0.0112, "memory": 0.12651453725993633, "temperature": 37, "efficiency": 65}, {"timestamp": 1749483596534, "cpu": 0.0081, "memory": 0.128951296210289, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749483598535, "cpu": 0.006600000000000001, "memory": 0.12757247313857079, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749483600535, "cpu": 0.0104, "memory": 0.12896787375211716, "temperature": 37, "efficiency": 64.5}, {"timestamp": 1749483602536, "cpu": 0.0098, "memory": 0.1278309617191553, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749483604538, "cpu": 0.011000000000000001, "memory": 0.1293404959142208, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749483606538, "cpu": 0.0089, "memory": 0.13078064657747746, "temperature": 37, "efficiency": 64}, {"timestamp": 1749483608539, "cpu": 0.0107, "memory": 0.129444757476449, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749483610539, "cpu": 0.006, "memory": 0.1318535301834345, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749483612540, "cpu": 0.0063, "memory": 0.12984592467546463, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749483614541, "cpu": 0.0060999999999999995, "memory": 0.1312047243118286, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749483616542, "cpu": 0.006200000000000001, "memory": 0.1295334193855524, "temperature": 37, "efficiency": 65}, {"timestamp": 1749483618543, "cpu": 0.0097, "memory": 0.13109599240124226, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749483620544, "cpu": 0.0083, "memory": 0.1333411317318678, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749483622545, "cpu": 0.0084, "memory": 0.1321053598076105, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749483624546, "cpu": 0.0093, "memory": 0.1325754914432764, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749483626547, "cpu": 0.009000000000000001, "memory": 0.13221041299402714, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749483628548, "cpu": 0.0065, "memory": 0.1346580684185028, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749483630550, "cpu": 0.0063999999999999994, "memory": 0.1322404481470585, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749483632550, "cpu": 0.009899999999999999, "memory": 0.1346880104392767, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749483634552, "cpu": 0.008, "memory": 0.1322985626757145, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749483636553, "cpu": 0.01, "memory": 0.13378090225160122, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749483638554, "cpu": 0.0077, "memory": 0.13615405187010765, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749483640555, "cpu": 0.0143, "memory": 0.1346131321042776, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749483642556, "cpu": 0.0086, "memory": 0.136161083355546, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749483644557, "cpu": 0.0077, "memory": 0.13364958576858044, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749483646558, "cpu": 0.0086, "memory": 0.13584578409790993, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749483648560, "cpu": 0.0073999999999999995, "memory": 0.13372008688747883, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749483650564, "cpu": 0.0164, "memory": 0.13591893948614597, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749483652566, "cpu": 0.0087, "memory": 0.13425028882920742, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749483654566, "cpu": 0.0136, "memory": 0.1347837969660759, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749483656567, "cpu": 0.012400000000000001, "memory": 0.13693845830857754, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749483658569, "cpu": 0.009399999999999999, "memory": 0.1350735779851675, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749483660570, "cpu": 0.0072, "memory": 0.13645589351654053, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749483662572, "cpu": 0.0071, "memory": 0.1389087177813053, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749483664573, "cpu": 0.0091, "memory": 0.13621049001812935, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749483666573, "cpu": 0.0086, "memory": 0.13762135058641434, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749483668575, "cpu": 0.0106, "memory": 0.13608280569314957, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749483670575, "cpu": 0.0071, "memory": 0.13825003989040852, "temperature": 37, "efficiency": 64}, {"timestamp": 1749483672576, "cpu": 0.0077, "memory": 0.14003869146108627, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749483674576, "cpu": 0.008, "memory": 0.1375298947095871, "temperature": 37, "efficiency": 65.7}, {"timestamp": 1749483676578, "cpu": 0.0060999999999999995, "memory": 0.13990621082484722, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749483678578, "cpu": 0.0060999999999999995, "memory": 0.1375621184706688, "temperature": 37, "efficiency": 64.4}, {"timestamp": 1749483680579, "cpu": 0.006600000000000001, "memory": 0.13969875872135162, "temperature": 37, "efficiency": 64}, {"timestamp": 1749483682581, "cpu": 0.006200000000000001, "memory": 0.13781427405774593, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749483684581, "cpu": 0.0095, "memory": 0.13834857381880283, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749483686582, "cpu": 0.0060999999999999995, "memory": 0.13836948201060295, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749483688584, "cpu": 0.006, "memory": 0.1409696415066719, "temperature": 37, "efficiency": 64}, {"timestamp": 1749483690585, "cpu": 0.0144, "memory": 0.14228695072233677, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749483692585, "cpu": 0.010799999999999999, "memory": 0.14028828591108322, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749483694586, "cpu": 0.0126, "memory": 0.14167502522468567, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749483696587, "cpu": 0.009000000000000001, "memory": 0.1430230215191841, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749483698588, "cpu": 0.009899999999999999, "memory": 0.14164173044264317, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749483700589, "cpu": 0.0166, "memory": 0.14382340013980865, "temperature": 37, "efficiency": 63.3}, {"timestamp": 1749483702590, "cpu": 0.0113, "memory": 0.141521031036973, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749483704591, "cpu": 0.0093, "memory": 0.14279978349804878, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749483706593, "cpu": 0.0137, "memory": 0.14099432155489922, "temperature": 37, "efficiency": 65.8}, {"timestamp": 1749483708593, "cpu": 0.0089, "memory": 0.1424853689968586, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749483710594, "cpu": 0.0092, "memory": 0.14468342997133732, "temperature": 37, "efficiency": 65}, {"timestamp": 1749483712595, "cpu": 0.0144, "memory": 0.14313189312815666, "temperature": 37, "efficiency": 64.1}, {"timestamp": 1749483714596, "cpu": 0.0142, "memory": 0.14366405084729195, "temperature": 37, "efficiency": 64}, {"timestamp": 1749483716597, "cpu": 0.01, "memory": 0.14580530114471912, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749483718598, "cpu": 0.013999999999999999, "memory": 0.14457297511398792, "temperature": 37, "efficiency": 63.8}, {"timestamp": 1749483720599, "cpu": 0.0112, "memory": 0.14594998210668564, "temperature": 37, "efficiency": 63.5}, {"timestamp": 1749483722600, "cpu": 0.0146, "memory": 0.14438694342970848, "temperature": 37, "efficiency": 64.2}, {"timestamp": 1749483724601, "cpu": 0.0162, "memory": 0.14570062048733234, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749483726602, "cpu": 0.015200000000000002, "memory": 0.14703688211739063, "temperature": 37, "efficiency": 63.6}, {"timestamp": 1749483728603, "cpu": 0.007899999999999999, "memory": 0.14564073644578457, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749483730605, "cpu": 0.0183, "memory": 0.14392444863915443, "temperature": 37, "efficiency": 64.3}, {"timestamp": 1749483732606, "cpu": 0.0136, "memory": 0.14552967622876167, "temperature": 37, "efficiency": 63.9}, {"timestamp": 1749483734608, "cpu": 0.0113, "memory": 0.14680749736726284, "temperature": 37, "efficiency": 63.7}, {"timestamp": 1749483736609, "cpu": 0.0073999999999999995, "memory": 0.14450028538703918, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749483738609, "cpu": 0.010799999999999999, "memory": 0.10284455493092537, "temperature": 37, "efficiency": 73.1}, {"timestamp": 1749483740611, "cpu": 0.01, "memory": 0.10507483966648579, "temperature": 37, "efficiency": 72.7}, {"timestamp": 1749483742611, "cpu": 0.0123, "memory": 0.10293452069163322, "temperature": 37, "efficiency": 73.1}, {"timestamp": 1749483744612, "cpu": 0.006, "memory": 0.10390849784016609, "temperature": 37, "efficiency": 72.9}, {"timestamp": 1749483746613, "cpu": 0.017, "memory": 0.10446971282362938, "temperature": 37, "efficiency": 72.8}, {"timestamp": 1749483748615, "cpu": 0.0169, "memory": 0.10727811604738235, "temperature": 37, "efficiency": 72.2}, {"timestamp": 1749483750615, "cpu": 0.0131, "memory": 0.10553449392318726, "temperature": 37, "efficiency": 72.6}, {"timestamp": 1749483752617, "cpu": 0.009899999999999999, "memory": 0.1078581903129816, "temperature": 37, "efficiency": 72.1}, {"timestamp": 1749483754618, "cpu": 0.02, "memory": 0.10526641272008419, "temperature": 37, "efficiency": 72.6}, {"timestamp": 1749483756619, "cpu": 0.0155, "memory": 0.10648882016539574, "temperature": 37, "efficiency": 72.4}, {"timestamp": 1749483758620, "cpu": 0.0164, "memory": 0.10869349353015423, "temperature": 37, "efficiency": 71.9}, {"timestamp": 1749483760621, "cpu": 0.0182, "memory": 0.10744566097855568, "temperature": 37, "efficiency": 72.1}], "alerts": [], "optimizations": [{"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}