{"timestamp": 1749470771886, "metrics": {"system": {"cpu": {"usage": 0.004699999999999999, "temperature": 37, "cores": 10}, "memory": {"used": 16039480, "total": 17179869184, "efficiency": 34.90876542760971}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749468971932}, "performance": {"uptime": 1798.942, "efficiency": 73.6, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749470572779, "cpu": 0.28609999999999997, "memory": 0.11885911226272583, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749470574781, "cpu": 0.293, "memory": 0.11897203512489796, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749470576781, "cpu": 0.0117, "memory": 0.11847368441522121, "temperature": 37, "efficiency": 66.9}, {"timestamp": 1749470578784, "cpu": 0.4406, "memory": 0.12060850858688354, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749470580784, "cpu": 0.0087, "memory": 0.11818017810583115, "temperature": 37, "efficiency": 67}, {"timestamp": 1749470582786, "cpu": 0.0146, "memory": 0.11880905367434025, "temperature": 37, "efficiency": 66.8}, {"timestamp": 1749470584788, "cpu": 0.8519000000000001, "memory": 0.11997977271676064, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749470586788, "cpu": 0.0081, "memory": 0.11874595656991005, "temperature": 37, "efficiency": 68}, {"timestamp": 1749470588788, "cpu": 0.010799999999999999, "memory": 0.119056086987257, "temperature": 37, "efficiency": 66.7}, {"timestamp": 1749470590789, "cpu": 0.8892000000000001, "memory": 0.12379060499370098, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749470592791, "cpu": 0.0127, "memory": 0.12113731354475021, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749470594794, "cpu": 0.4497, "memory": 0.12054177932441235, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749470596793, "cpu": 0.3703, "memory": 0.12069130316376686, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749470598796, "cpu": 0.015799999999999998, "memory": 0.12142574414610863, "temperature": 37, "efficiency": 66.2}, {"timestamp": 1749470600797, "cpu": 0.013300000000000001, "memory": 0.12407740578055382, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749470602798, "cpu": 0.5781, "memory": 0.12181433849036694, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749470604797, "cpu": 0.49979999999999997, "memory": 0.12174132280051708, "temperature": 37, "efficiency": 65.9}, {"timestamp": 1749470606799, "cpu": 0.009600000000000001, "memory": 0.12222826480865479, "temperature": 37, "efficiency": 66}, {"timestamp": 1749470608799, "cpu": 0.5579, "memory": 0.12414222583174706, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749470610799, "cpu": 0.008, "memory": 0.12195482850074768, "temperature": 37, "efficiency": 66.1}, {"timestamp": 1749470612800, "cpu": 0.009399999999999999, "memory": 0.12256656773388386, "temperature": 37, "efficiency": 67.1}, {"timestamp": 1749470614800, "cpu": 0.6822, "memory": 0.12404881417751312, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749470616801, "cpu": 0.02, "memory": 0.12683137319982052, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749470618801, "cpu": 0.0222, "memory": 0.12719077058136463, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749470620802, "cpu": 0.39830000000000004, "memory": 0.12749633751809597, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749470622803, "cpu": 0.0116, "memory": 0.12454027310013771, "temperature": 37, "efficiency": 65.4}, {"timestamp": 1749470624802, "cpu": 0.2599, "memory": 0.12382799759507179, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749470626805, "cpu": 0.4618, "memory": 0.12380611151456833, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749470628805, "cpu": 0.011000000000000001, "memory": 0.12433542869985104, "temperature": 37, "efficiency": 65.5}, {"timestamp": 1749470630807, "cpu": 0.0103, "memory": 0.1271114218980074, "temperature": 37, "efficiency": 64.8}, {"timestamp": 1749470632806, "cpu": 0.20779999999999998, "memory": 0.124770263209939, "temperature": 37, "efficiency": 65.3}, {"timestamp": 1749470634808, "cpu": 0.6954, "memory": 0.12518330477178097, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749470636809, "cpu": 0.107, "memory": 0.12885481119155884, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749470638810, "cpu": 0.38539999999999996, "memory": 0.12642736546695232, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749470640811, "cpu": 0.0087, "memory": 0.1252366229891777, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749470642813, "cpu": 0.0083, "memory": 0.12633618898689747, "temperature": 37, "efficiency": 66.6}, {"timestamp": 1749470644812, "cpu": 0.8576999999999999, "memory": 0.12779035605490208, "temperature": 37, "efficiency": 66}, {"timestamp": 1749470646815, "cpu": 0.006, "memory": 0.12724921107292175, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749470648816, "cpu": 0.0174, "memory": 0.12758527882397175, "temperature": 37, "efficiency": 65}, {"timestamp": 1749470650817, "cpu": 0.0091, "memory": 0.12712334282696247, "temperature": 37, "efficiency": 65.1}, {"timestamp": 1749470652818, "cpu": 0.0085, "memory": 0.1290131825953722, "temperature": 37, "efficiency": 64.7}, {"timestamp": 1749470654819, "cpu": 0.0162, "memory": 0.12829015031456947, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749470656820, "cpu": 0.0127, "memory": 0.1282219309359789, "temperature": 37, "efficiency": 64.9}, {"timestamp": 1749470658822, "cpu": 0.0044, "memory": 0.12952303513884544, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749470660821, "cpu": 0.0117, "memory": 0.1281058881431818, "temperature": 37, "efficiency": 65.2}, {"timestamp": 1749470662821, "cpu": 0.12229999999999999, "memory": 0.130790239199996, "temperature": 37, "efficiency": 64.6}, {"timestamp": 1749470664823, "cpu": 0.012400000000000001, "memory": 0.12804237194359303, "temperature": 37, "efficiency": 65.6}, {"timestamp": 1749470666823, "cpu": 0.0115, "memory": 0.08683521300554276, "temperature": 37, "efficiency": 75.1}, {"timestamp": 1749470668823, "cpu": 0.0144, "memory": 0.08863573893904686, "temperature": 37, "efficiency": 74.7}, {"timestamp": 1749470670826, "cpu": 0.0179, "memory": 0.0877805519849062, "temperature": 37, "efficiency": 74.9}, {"timestamp": 1749470672826, "cpu": 0.0088, "memory": 0.08843783289194107, "temperature": 37, "efficiency": 74.8}, {"timestamp": 1749470674827, "cpu": 0.0051, "memory": 0.08990135975182056, "temperature": 37, "efficiency": 74.4}, {"timestamp": 1749470676827, "cpu": 0.005, "memory": 0.08827988058328629, "temperature": 37, "efficiency": 74.8}, {"timestamp": 1749470678829, "cpu": 0.0049, "memory": 0.0882850494235754, "temperature": 37, "efficiency": 74.8}, {"timestamp": 1749470680829, "cpu": 0.005, "memory": 0.08809641003608704, "temperature": 37, "efficiency": 74.9}, {"timestamp": 1749470682831, "cpu": 0.0073, "memory": 0.09126798249781132, "temperature": 37, "efficiency": 74.1}, {"timestamp": 1749470684831, "cpu": 0.0055000000000000005, "memory": 0.09031277149915695, "temperature": 37, "efficiency": 74.3}, {"timestamp": 1749470686833, "cpu": 0.0046, "memory": 0.09073726832866669, "temperature": 37, "efficiency": 74.2}, {"timestamp": 1749470688833, "cpu": 0.005, "memory": 0.09066197089850903, "temperature": 37, "efficiency": 74.3}, {"timestamp": 1749470690835, "cpu": 0.006, "memory": 0.08898153901100159, "temperature": 37, "efficiency": 75.5}, {"timestamp": 1749470692836, "cpu": 0.0065, "memory": 0.09090304374694824, "temperature": 37, "efficiency": 75.1}, {"timestamp": 1749470694837, "cpu": 0.004, "memory": 0.09162360802292824, "temperature": 37, "efficiency": 74}, {"timestamp": 1749470696838, "cpu": 0.0046, "memory": 0.08944072760641575, "temperature": 37, "efficiency": 74.5}, {"timestamp": 1749470698839, "cpu": 0.004699999999999999, "memory": 0.09177639149129391, "temperature": 37, "efficiency": 74}, {"timestamp": 1749470700840, "cpu": 0.004699999999999999, "memory": 0.09275879710912704, "temperature": 37, "efficiency": 73.8}, {"timestamp": 1749470702842, "cpu": 0.0048000000000000004, "memory": 0.093524856492877, "temperature": 37, "efficiency": 73.6}, {"timestamp": 1749470704842, "cpu": 0.0043, "memory": 0.09027598425745964, "temperature": 37, "efficiency": 74.4}, {"timestamp": 1749470706844, "cpu": 0.0071, "memory": 0.09382092393934727, "temperature": 37, "efficiency": 73.5}, {"timestamp": 1749470708844, "cpu": 0.0052, "memory": 0.09065265767276287, "temperature": 37, "efficiency": 74.3}, {"timestamp": 1749470710845, "cpu": 0.0048000000000000004, "memory": 0.09203697554767132, "temperature": 37, "efficiency": 73.9}, {"timestamp": 1749470712845, "cpu": 0.0041, "memory": 0.0910650473088026, "temperature": 37, "efficiency": 74.2}, {"timestamp": 1749470714847, "cpu": 0.0048000000000000004, "memory": 0.09208479896187782, "temperature": 37, "efficiency": 73.9}, {"timestamp": 1749470716848, "cpu": 0.0044, "memory": 0.09338944219052792, "temperature": 37, "efficiency": 73.6}, {"timestamp": 1749470718849, "cpu": 0.0044, "memory": 0.09054439142346382, "temperature": 37, "efficiency": 74.3}, {"timestamp": 1749470720849, "cpu": 0.004699999999999999, "memory": 0.0912986695766449, "temperature": 37, "efficiency": 74.1}, {"timestamp": 1749470722850, "cpu": 0.0073, "memory": 0.09371521882712841, "temperature": 37, "efficiency": 73.6}, {"timestamp": 1749470724852, "cpu": 0.005, "memory": 0.0947462860494852, "temperature": 37, "efficiency": 73.3}, {"timestamp": 1749470726852, "cpu": 0.0041, "memory": 0.09172540158033371, "temperature": 37, "efficiency": 74}, {"timestamp": 1749470728853, "cpu": 0.0045000000000000005, "memory": 0.09379126131534576, "temperature": 37, "efficiency": 73.5}, {"timestamp": 1749470730855, "cpu": 0.005, "memory": 0.09532771073281765, "temperature": 37, "efficiency": 73.2}, {"timestamp": 1749470732856, "cpu": 0.006200000000000001, "memory": 0.09212056174874306, "temperature": 37, "efficiency": 73.9}, {"timestamp": 1749470734856, "cpu": 0.004, "memory": 0.09360681287944317, "temperature": 37, "efficiency": 73.6}, {"timestamp": 1749470736857, "cpu": 0.0067, "memory": 0.09514945559203625, "temperature": 37, "efficiency": 73.2}, {"timestamp": 1749470738860, "cpu": 0.0063999999999999994, "memory": 0.09207092225551605, "temperature": 37, "efficiency": 73.9}, {"timestamp": 1749470740860, "cpu": 0.0049, "memory": 0.09347964078187943, "temperature": 37, "efficiency": 73.6}, {"timestamp": 1749470742863, "cpu": 0.004699999999999999, "memory": 0.09273453615605831, "temperature": 37, "efficiency": 73.8}, {"timestamp": 1749470744863, "cpu": 0.005, "memory": 0.0941139180213213, "temperature": 37, "efficiency": 73.5}, {"timestamp": 1749470746864, "cpu": 0.0049, "memory": 0.09562396444380283, "temperature": 37, "efficiency": 73.1}, {"timestamp": 1749470748865, "cpu": 0.0052, "memory": 0.09280857630074024, "temperature": 37, "efficiency": 73.8}, {"timestamp": 1749470750867, "cpu": 0.007899999999999999, "memory": 0.09422283619642258, "temperature": 37, "efficiency": 73.4}, {"timestamp": 1749470752868, "cpu": 0.0049, "memory": 0.09659407660365105, "temperature": 37, "efficiency": 72.9}, {"timestamp": 1749470754868, "cpu": 0.0038000000000000004, "memory": 0.09353719651699066, "temperature": 37, "efficiency": 73.6}, {"timestamp": 1749470756868, "cpu": 0.0043, "memory": 0.09469268843531609, "temperature": 37, "efficiency": 73.3}, {"timestamp": 1749470758869, "cpu": 0.0179, "memory": 0.09689964354038239, "temperature": 37, "efficiency": 72.8}, {"timestamp": 1749470760870, "cpu": 0.005, "memory": 0.09392458014190197, "temperature": 37, "efficiency": 73.5}, {"timestamp": 1749470762871, "cpu": 0.0069, "memory": 0.09650508873164654, "temperature": 37, "efficiency": 72.9}, {"timestamp": 1749470764871, "cpu": 0.005, "memory": 0.09305696003139019, "temperature": 37, "efficiency": 73.7}, {"timestamp": 1749470766873, "cpu": 0.0095, "memory": 0.09476710110902786, "temperature": 37, "efficiency": 73.3}, {"timestamp": 1749470768873, "cpu": 0.004699999999999999, "memory": 0.0961830373853445, "temperature": 37, "efficiency": 73}, {"timestamp": 1749470770874, "cpu": 0.004699999999999999, "memory": 0.09336206130683422, "temperature": 37, "efficiency": 73.6}], "alerts": [], "optimizations": [{"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}