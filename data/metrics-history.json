{"timestamp": 1749481917559, "metrics": {"system": {"cpu": {"usage": 0.0085, "temperature": 37, "cores": 10}, "memory": {"used": 20019808, "total": 17179869184, "efficiency": 17.158501059322035}, "disk": {"usage": 0, "speed": 100, "available": 0}, "network": {"latency": 0, "throughput": 100, "connections": 0}}, "ai": {"neurons": 0, "qi": {"agent": 100, "memory": 58, "combined": 158}, "responses": {"total": 0, "quality": 95, "speed": 100}, "learning": {"sessions": 0, "improvement": 0, "accuracy": 95}}, "security": {"threats": 0, "scans": 0, "status": "secure", "lastScan": 1749481317527}, "performance": {"uptime": 599.8, "efficiency": 67.7, "optimization": 100, "stability": 100}, "network": {"latency": 2, "throughput": 100, "connections": 0}}, "history": [{"timestamp": 1749481719227, "cpu": 0.0779, "memory": 0.10745781473815441, "temperature": 37, "efficiency": 69.8}, {"timestamp": 1749481721227, "cpu": 0.051500000000000004, "memory": 0.10460247285664082, "temperature": 37, "efficiency": 70.5}, {"timestamp": 1749481723229, "cpu": 0.12869999999999998, "memory": 0.10528634302318096, "temperature": 37, "efficiency": 70.3}, {"timestamp": 1749481725228, "cpu": 0.10120000000000001, "memory": 0.10722018778324127, "temperature": 37, "efficiency": 69.9}, {"timestamp": 1749481727230, "cpu": 0.1041, "memory": 0.10792757384479046, "temperature": 37, "efficiency": 69.7}, {"timestamp": 1749481729230, "cpu": 0.1503, "memory": 0.10566450655460358, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749481731232, "cpu": 0.11069999999999999, "memory": 0.10617612861096859, "temperature": 37, "efficiency": 70.1}, {"timestamp": 1749481733232, "cpu": 0.1312, "memory": 0.10672640055418015, "temperature": 37, "efficiency": 70}, {"timestamp": 1749481735233, "cpu": 0.0628, "memory": 0.10834243148565292, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749481737234, "cpu": 0.11249999999999999, "memory": 0.1049380749464035, "temperature": 37, "efficiency": 70.4}, {"timestamp": 1749481739235, "cpu": 0.1343, "memory": 0.10656090453267097, "temperature": 37, "efficiency": 70}, {"timestamp": 1749481741238, "cpu": 0.0924, "memory": 0.10730219073593616, "temperature": 37, "efficiency": 69.9}, {"timestamp": 1749481743238, "cpu": 0.1874, "memory": 0.1078141387552023, "temperature": 37, "efficiency": 69.7}, {"timestamp": 1749481745240, "cpu": 0.1535, "memory": 0.10584313422441483, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749481747240, "cpu": 0.0383, "memory": 0.10682456195354462, "temperature": 37, "efficiency": 70}, {"timestamp": 1749481749241, "cpu": 0.1243, "memory": 0.1084489282220602, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749481751243, "cpu": 0.167, "memory": 0.10920073837041855, "temperature": 37, "efficiency": 69.4}, {"timestamp": 1749481753242, "cpu": 0.098, "memory": 0.10575689375400543, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749481755243, "cpu": 0.11199999999999999, "memory": 0.10751471854746342, "temperature": 37, "efficiency": 69.8}, {"timestamp": 1749481757244, "cpu": 0.0869, "memory": 0.10825074277818203, "temperature": 37, "efficiency": 69.7}, {"timestamp": 1749481759245, "cpu": 0.13649999999999998, "memory": 0.10986458510160446, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749481761246, "cpu": 0.0365, "memory": 0.10646339505910873, "temperature": 37, "efficiency": 70.1}, {"timestamp": 1749481763247, "cpu": 0.0772, "memory": 0.10700882412493229, "temperature": 37, "efficiency": 70}, {"timestamp": 1749481765249, "cpu": 0.12329999999999999, "memory": 0.10653422214090824, "temperature": 37, "efficiency": 71.1}, {"timestamp": 1749481767250, "cpu": 0.0799, "memory": 0.10709594935178757, "temperature": 37, "efficiency": 71}, {"timestamp": 1749481769250, "cpu": 0.0605, "memory": 0.10929307900369167, "temperature": 37, "efficiency": 70.5}, {"timestamp": 1749481771251, "cpu": 0.0427, "memory": 0.10992716997861862, "temperature": 37, "efficiency": 70.4}, {"timestamp": 1749481773252, "cpu": 0.0484, "memory": 0.1105697825551033, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749481775254, "cpu": 0.0561, "memory": 0.10788743384182453, "temperature": 37, "efficiency": 69.7}, {"timestamp": 1749481777254, "cpu": 0.6376999999999999, "memory": 0.10841772891581059, "temperature": 37, "efficiency": 69.4}, {"timestamp": 1749481779256, "cpu": 0.1037, "memory": 0.11023408733308315, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749481781257, "cpu": 0.24130000000000001, "memory": 0.11127004399895668, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749481783258, "cpu": 0.2568, "memory": 0.1080765388906002, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749481785258, "cpu": 0.0599, "memory": 0.10988237336277962, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749481787259, "cpu": 0.0749, "memory": 0.11039557866752148, "temperature": 37, "efficiency": 69.1}, {"timestamp": 1749481789261, "cpu": 0.1591, "memory": 0.11200439184904099, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749481791262, "cpu": 0.1156, "memory": 0.10846033692359924, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749481793261, "cpu": 0.1186, "memory": 0.10950644500553608, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749481795263, "cpu": 0.09970000000000001, "memory": 0.11108750477433205, "temperature": 37, "efficiency": 69}, {"timestamp": 1749481797265, "cpu": 0.0733, "memory": 0.11177961714565754, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749481799267, "cpu": 0.05689999999999999, "memory": 0.10961461812257767, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749481801267, "cpu": 0.0642, "memory": 0.11137705296278, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749481803269, "cpu": 0.062200000000000005, "memory": 0.11206092312932014, "temperature": 37, "efficiency": 68.8}, {"timestamp": 1749481805270, "cpu": 0.0393, "memory": 0.1094035804271698, "temperature": 37, "efficiency": 69.4}, {"timestamp": 1749481807270, "cpu": 0.13979999999999998, "memory": 0.10991240851581097, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749481809271, "cpu": 0.1732, "memory": 0.11248048394918442, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749481811289, "cpu": 0.47080000000000005, "memory": 0.11302349157631397, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749481813274, "cpu": 0.0397, "memory": 0.10985876433551311, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749481815277, "cpu": 0.0867, "memory": 0.11155442334711552, "temperature": 37, "efficiency": 68.9}, {"timestamp": 1749481817276, "cpu": 0.1377, "memory": 0.11206506751477718, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749481819277, "cpu": 0.0276, "memory": 0.11392608284950256, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749481821280, "cpu": 0.1355, "memory": 0.11056209914386272, "temperature": 37, "efficiency": 69.1}, {"timestamp": 1749481823280, "cpu": 0.0071, "memory": 0.1113145612180233, "temperature": 37, "efficiency": 69}, {"timestamp": 1749481825281, "cpu": 0.008, "memory": 0.11080116964876652, "temperature": 37, "efficiency": 70.2}, {"timestamp": 1749481827282, "cpu": 0.0055000000000000005, "memory": 0.11155647225677967, "temperature": 37, "efficiency": 70}, {"timestamp": 1749481829283, "cpu": 0.0112, "memory": 0.11335136368870735, "temperature": 37, "efficiency": 69.6}, {"timestamp": 1749481831283, "cpu": 0.0156, "memory": 0.11409292928874493, "temperature": 37, "efficiency": 69.4}, {"timestamp": 1749481833285, "cpu": 0.0078, "memory": 0.11476301588118076, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749481835286, "cpu": 0.0071, "memory": 0.11220979504287243, "temperature": 37, "efficiency": 68.7}, {"timestamp": 1749481837286, "cpu": 0.0114, "memory": 0.11272192932665348, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749481839288, "cpu": 0.006600000000000001, "memory": 0.11428785510361195, "temperature": 37, "efficiency": 68.2}, {"timestamp": 1749481841288, "cpu": 0.019200000000000002, "memory": 0.11483551934361458, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749481843291, "cpu": 0.0644, "memory": 0.11540353298187256, "temperature": 37, "efficiency": 68}, {"timestamp": 1749481845291, "cpu": 0.0151, "memory": 0.11276588775217533, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749481847292, "cpu": 0.0113, "memory": 0.11327872052788734, "temperature": 37, "efficiency": 68.5}, {"timestamp": 1749481849293, "cpu": 0.006999999999999999, "memory": 0.11484813876450062, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749481851296, "cpu": 0.0057, "memory": 0.11560190469026566, "temperature": 37, "efficiency": 67.9}, {"timestamp": 1749481853297, "cpu": 0.026400000000000003, "memory": 0.11260961182415485, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749481855298, "cpu": 0.0107, "memory": 0.11435667984187603, "temperature": 37, "efficiency": 68.2}, {"timestamp": 1749481857298, "cpu": 0.0102, "memory": 0.11490415781736374, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749481859301, "cpu": 0.0213, "memory": 0.11651287786662579, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749481861302, "cpu": 0.050199999999999995, "memory": 0.11324603110551834, "temperature": 37, "efficiency": 68.5}, {"timestamp": 1749481863301, "cpu": 0.0134, "memory": 0.11377553455531597, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749481865303, "cpu": 0.0063999999999999994, "memory": 0.11550136841833591, "temperature": 37, "efficiency": 68}, {"timestamp": 1749481867305, "cpu": 0.040499999999999994, "memory": 0.11630584485828876, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749481869305, "cpu": 0.026600000000000002, "memory": 0.1139465719461441, "temperature": 37, "efficiency": 68.3}, {"timestamp": 1749481871306, "cpu": 0.0328, "memory": 0.11476622894406319, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749481873308, "cpu": 0.019799999999999998, "memory": 0.11543799191713333, "temperature": 37, "efficiency": 68}, {"timestamp": 1749481875308, "cpu": 0.0203, "memory": 0.11712564155459404, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749481877309, "cpu": 0.0436, "memory": 0.11372589506208897, "temperature": 37, "efficiency": 68.4}, {"timestamp": 1749481879310, "cpu": 0.0073999999999999995, "memory": 0.11554663069546223, "temperature": 37, "efficiency": 68}, {"timestamp": 1749481881312, "cpu": 0.0368, "memory": 0.11626486666500568, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749481883313, "cpu": 0.0121, "memory": 0.11680140160024166, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749481885313, "cpu": 0.5403, "memory": 0.1141995657235384, "temperature": 37, "efficiency": 68.1}, {"timestamp": 1749481887315, "cpu": 0.0136, "memory": 0.11664708144962788, "temperature": 37, "efficiency": 67.7}, {"timestamp": 1749481889316, "cpu": 0.048799999999999996, "memory": 0.11461232788860798, "temperature": 37, "efficiency": 69.3}, {"timestamp": 1749481891317, "cpu": 0.0571, "memory": 0.11515626683831215, "temperature": 37, "efficiency": 69.2}, {"timestamp": 1749481893317, "cpu": 0.0119, "memory": 0.11570341885089874, "temperature": 37, "efficiency": 69.1}, {"timestamp": 1749481895318, "cpu": 0.0246, "memory": 0.11748946271836758, "temperature": 37, "efficiency": 68.6}, {"timestamp": 1749481897325, "cpu": 0.0491, "memory": 0.11799805797636509, "temperature": 37, "efficiency": 68.5}, {"timestamp": 1749481899320, "cpu": 0.0077, "memory": 0.1153421588242054, "temperature": 37, "efficiency": 68}, {"timestamp": 1749481901321, "cpu": 0.0265, "memory": 0.11616251431405544, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749481903321, "cpu": 0.0116, "memory": 0.11714966967701912, "temperature": 37, "efficiency": 67.6}, {"timestamp": 1749481905322, "cpu": 0.007600000000000001, "memory": 0.11869431473314762, "temperature": 37, "efficiency": 67.2}, {"timestamp": 1749481907323, "cpu": 0.013, "memory": 0.1154816709458828, "temperature": 37, "efficiency": 68}, {"timestamp": 1749481909324, "cpu": 0.0086, "memory": 0.11734920553863049, "temperature": 37, "efficiency": 67.5}, {"timestamp": 1749481911326, "cpu": 0.0112, "memory": 0.11792546138167381, "temperature": 37, "efficiency": 67.4}, {"timestamp": 1749481913325, "cpu": 0.012899999999999998, "memory": 0.11845901608467102, "temperature": 37, "efficiency": 67.3}, {"timestamp": 1749481915326, "cpu": 0.0092, "memory": 0.11602221056818962, "temperature": 37, "efficiency": 67.8}, {"timestamp": 1749481917327, "cpu": 0.0085, "memory": 0.11653061956167221, "temperature": 37, "efficiency": 67.7}], "alerts": [], "optimizations": [{"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}, {"type": "CACHE_OPTIMIZATION", "message": "<PERSON><PERSON> opti<PERSON>", "impact": "Amélioration des performances"}, {"type": "CONNECTION_OPTIMIZATION", "message": "Connexions optimisées", "impact": "R<PERSON><PERSON> de la latence"}]}