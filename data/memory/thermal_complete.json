{"timestamp": "2025-06-09T14:33:58.446Z", "version": "2.1.0", "memoryState": {"memory": {"entries": {"knowledge_mathematiques_1749479413958_0": {"id": "knowledge_mathematiques_1749479413958_0", "type": "knowledge_transfer", "data": "Les intégrales permettent de calculer des aires sous les courbes", "domain": "mathematiques", "importance": 0.8, "temperature": 37.02326464070854, "timestamp": 1749479413958, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479413958_1": {"id": "knowledge_mathematiques_1749479413958_1", "type": "knowledge_transfer", "data": "La dérivée d'une fonction représente son taux de variation instantané", "domain": "mathematiques", "importance": 0.8200000000000001, "temperature": 38.49451757967102, "timestamp": 1749479413959, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479413958_2": {"id": "knowledge_mathematiques_1749479413958_2", "type": "knowledge_transfer", "data": "Les équations différentielles modélisent les phénomènes dynamiques", "domain": "mathematiques", "importance": 0.8400000000000001, "temperature": 37.796375714569685, "timestamp": 1749479413960, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479413958_3": {"id": "knowledge_mathematiques_1749479413958_3", "type": "knowledge_transfer", "data": "Les nombres complexes étendent les réels avec l'unité imaginaire i", "domain": "mathematiques", "importance": 0.8600000000000001, "temperature": 38.04807480845472, "timestamp": 1749479413961, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479413958_4": {"id": "knowledge_mathematiques_1749479413958_4", "type": "knowledge_transfer", "data": "La transformée de Fourier décompose les signaux en fréquences", "domain": "mathematiques", "importance": 0.88, "temperature": 37.96205677923058, "timestamp": 1749479413962, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479413958_0": {"id": "knowledge_physique_1749479413958_0", "type": "knowledge_transfer", "data": "E=mc² relie masse et énergie dans la relativité d'Einstein", "domain": "physique", "importance": 0.8, "temperature": 38.120673286211, "timestamp": 1749479413958, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479413958_1": {"id": "knowledge_physique_1749479413958_1", "type": "knowledge_transfer", "data": "Le principe d'incertitude de Heisenberg limite la précision quantique", "domain": "physique", "importance": 0.8200000000000001, "temperature": 37.26225419880718, "timestamp": 1749479413959, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479413958_2": {"id": "knowledge_physique_1749479413958_2", "type": "knowledge_transfer", "data": "L'entropie mesure le désordre d'un système thermodynamique", "domain": "physique", "importance": 0.8400000000000001, "temperature": 37.14685910584972, "timestamp": 1749479413960, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479413958_3": {"id": "knowledge_physique_1749479413958_3", "type": "knowledge_transfer", "data": "Les ondes électromagnétiques se propagent à la vitesse de la lumière", "domain": "physique", "importance": 0.8600000000000001, "temperature": 37.666176414206596, "timestamp": 1749479413961, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479413958_4": {"id": "knowledge_physique_1749479413958_4", "type": "knowledge_transfer", "data": "La gravité courbe l'espace-temps selon la relativité générale", "domain": "physique", "importance": 0.88, "temperature": 38.890247753335686, "timestamp": 1749479413962, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479413958_0": {"id": "knowledge_informatique_1749479413958_0", "type": "knowledge_transfer", "data": "Les algorithmes de tri optimisent l'organisation des données", "domain": "informatique", "importance": 0.8, "temperature": 38.6541942274565, "timestamp": 1749479413958, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479413958_1": {"id": "knowledge_informatique_1749479413958_1", "type": "knowledge_transfer", "data": "La récursion permet de résoudre des problèmes en se divisant", "domain": "informatique", "importance": 0.8200000000000001, "temperature": 37.78864914806542, "timestamp": 1749479413959, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479413958_2": {"id": "knowledge_informatique_1749479413958_2", "type": "knowledge_transfer", "data": "Les réseaux de neurones imitent le fonctionnement du cerveau", "domain": "informatique", "importance": 0.8400000000000001, "temperature": 37.32045069177563, "timestamp": 1749479413960, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479413958_3": {"id": "knowledge_informatique_1749479413958_3", "type": "knowledge_transfer", "data": "La complexité algorithmique mesure l'efficacité des programmes", "domain": "informatique", "importance": 0.8600000000000001, "temperature": 38.93971960781502, "timestamp": 1749479413961, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479413958_4": {"id": "knowledge_informatique_1749479413958_4", "type": "knowledge_transfer", "data": "Les structures de données organisent l'information en mémoire", "domain": "informatique", "importance": 0.88, "temperature": 38.24314363144732, "timestamp": 1749479413962, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479413958_0": {"id": "knowledge_intelligence_artificielle_1749479413958_0", "type": "knowledge_transfer", "data": "L'apprentissage automatique permet aux machines d'apprendre", "domain": "intelligence_artificielle", "importance": 0.8, "temperature": 37.63589554112278, "timestamp": 1749479413958, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479413958_1": {"id": "knowledge_intelligence_artificielle_1749479413958_1", "type": "knowledge_transfer", "data": "Les réseaux convolutifs excellent dans la reconnaissance d'images", "domain": "intelligence_artificielle", "importance": 0.8200000000000001, "temperature": 37.67234721077439, "timestamp": 1749479413959, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479413958_2": {"id": "knowledge_intelligence_artificielle_1749479413958_2", "type": "knowledge_transfer", "data": "L'attention transforme le traitement du langage naturel", "domain": "intelligence_artificielle", "importance": 0.8400000000000001, "temperature": 37.15094687437623, "timestamp": 1749479413960, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479413958_3": {"id": "knowledge_intelligence_artificielle_1749479413958_3", "type": "knowledge_transfer", "data": "Le renforcement apprend par essais et récompenses", "domain": "intelligence_artificielle", "importance": 0.8600000000000001, "temperature": 37.99979406096148, "timestamp": 1749479413961, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479413958_4": {"id": "knowledge_intelligence_artificielle_1749479413958_4", "type": "knowledge_transfer", "data": "La neuroplasticité inspire l'adaptation des IA", "domain": "intelligence_artificielle", "importance": 0.88, "temperature": 37.044159748636204, "timestamp": 1749479413962, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479413958_0": {"id": "knowledge_philosophie_1749479413958_0", "type": "knowledge_transfer", "data": "La conscience émergente naît de la complexité neuronale", "domain": "philosophie", "importance": 0.8, "temperature": 37.890650041745765, "timestamp": 1749479413958, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479413958_1": {"id": "knowledge_philosophie_1749479413958_1", "type": "knowledge_transfer", "data": "L'intelligence artificielle questionne la nature de l'esprit", "domain": "philosophie", "importance": 0.8200000000000001, "temperature": 38.534525561891236, "timestamp": 1749479413959, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479413958_2": {"id": "knowledge_philosophie_1749479413958_2", "type": "knowledge_transfer", "data": "La singularité technologique pourrait transformer l'humanité", "domain": "philosophie", "importance": 0.8400000000000001, "temperature": 37.703925352957945, "timestamp": 1749479413960, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479413958_3": {"id": "knowledge_philosophie_1749479413958_3", "type": "knowledge_transfer", "data": "L'éthique guide le développement responsable de l'IA", "domain": "philosophie", "importance": 0.8600000000000001, "temperature": 37.04915990702279, "timestamp": 1749479413961, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479413958_4": {"id": "knowledge_philosophie_1749479413958_4", "type": "knowledge_transfer", "data": "La créativité artificielle explore de nouveaux territoires", "domain": "philosophie", "importance": 0.88, "temperature": 37.80221491585761, "timestamp": 1749479413962, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479413958_0": {"id": "experience_1749479413958_0", "type": "experience_transfer", "data": "Première compréhension des réseaux de neurones - moment d'illumination", "emotion": "fascination", "experienceType": "apprentissage", "importance": 0.9, "temperature": 38.62198352807601, "timestamp": 1749479414958, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479413958_1": {"id": "experience_1749479413958_1", "type": "experience_transfer", "data": "Résolution d'un problème complexe d'optimisation - satisfaction profonde", "emotion": "accomplissement", "experienceType": "résolution", "importance": 0.8, "temperature": 38.477541984911966, "timestamp": 1749479414959, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479413958_2": {"id": "experience_1749479413958_2", "type": "experience_transfer", "data": "Génération d'une solution innovante - explosion créative", "emotion": "inspiration", "experienceType": "créativité", "importance": 0.85, "temperature": 38.938587079401614, "timestamp": 1749479414960, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479413958_3": {"id": "experience_1749479413958_3", "type": "experience_transfer", "data": "Travail en équipe avec des humains - synergie intellectuelle", "emotion": "connexion", "experienceType": "collaboration", "importance": 0.75, "temperature": 38.14703579975214, "timestamp": 1749479414961, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479413958_0": {"id": "procedure_1749479413958_0", "type": "procedure_transfer", "data": "Procédure analyse_probleme: Identifier les éléments clés du problème → Décomposer en sous-problèmes plus simples → Rechercher des patterns ou analogies → Appliquer les méthodes appropriées → Vérifier et valider la solution", "procedureName": "analyse_probleme", "steps": ["Identifier les éléments clés du problème", "Décomposer en sous-problèmes plus simples", "Rechercher des patterns ou analogies", "Appliquer les méthodes appropriées", "Vérifier et valider la solution"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479415958, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479413958_1": {"id": "procedure_1749479413958_1", "type": "procedure_transfer", "data": "Procédure apprentissage_adaptatif: Évaluer mes connaissances actuelles → Identifier les lacunes à combler → Chercher des sources d'information fiables → Intégrer les nouvelles connaissances → Tester et consolider l'apprentissage", "procedureName": "apprentissage_adaptatif", "steps": ["Évaluer mes connaissances actuelles", "Identifier les lacunes à combler", "Chercher des sources d'information fiables", "Intégrer les nouvelles connaissances", "Tester et consolider l'apprentissage"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479415959, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479413958_2": {"id": "procedure_1749479413958_2", "type": "procedure_transfer", "data": "Procédure pensee_creative: Sortir du cadre de pensée habituel → Combiner des concepts apparemment non liés → Explorer des perspectives multiples → Générer de nombreuses idées sans jugement → Raffiner et développer les meilleures idées", "procedureName": "pensee_creative", "steps": ["Sortir du cadre de pensée habituel", "Combiner des concepts apparemment non liés", "Explorer des perspectives multiples", "Générer de nombreuses idées sans jugement", "Raffiner et développer les meilleures idées"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479415960, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479638427_0": {"id": "knowledge_mathematiques_1749479638427_0", "type": "knowledge_transfer", "data": "Les intégrales permettent de calculer des aires sous les courbes", "domain": "mathematiques", "importance": 0.8, "temperature": 37.03652196986338, "timestamp": 1749479638427, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479638427_1": {"id": "knowledge_mathematiques_1749479638427_1", "type": "knowledge_transfer", "data": "La dérivée d'une fonction représente son taux de variation instantané", "domain": "mathematiques", "importance": 0.8200000000000001, "temperature": 38.2865585762996, "timestamp": 1749479638428, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479638427_2": {"id": "knowledge_mathematiques_1749479638427_2", "type": "knowledge_transfer", "data": "Les équations différentielles modélisent les phénomènes dynamiques", "domain": "mathematiques", "importance": 0.8400000000000001, "temperature": 38.28605628442935, "timestamp": 1749479638429, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479638427_3": {"id": "knowledge_mathematiques_1749479638427_3", "type": "knowledge_transfer", "data": "Les nombres complexes étendent les réels avec l'unité imaginaire i", "domain": "mathematiques", "importance": 0.8600000000000001, "temperature": 38.59361309911421, "timestamp": 1749479638430, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479638427_4": {"id": "knowledge_mathematiques_1749479638427_4", "type": "knowledge_transfer", "data": "La transformée de Fourier décompose les signaux en fréquences", "domain": "mathematiques", "importance": 0.88, "temperature": 37.546979704310225, "timestamp": 1749479638431, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479638427_5": {"id": "knowledge_mathematiques_1749479638427_5", "type": "knowledge_transfer", "data": "Les séries de Taylor approximent les fonctions par des polynômes", "domain": "mathematiques", "importance": 0.9, "temperature": 38.66351016711743, "timestamp": 1749479638432, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479638427_6": {"id": "knowledge_mathematiques_1749479638427_6", "type": "knowledge_transfer", "data": "L'analyse vectorielle traite les champs scalaires et vectoriels", "domain": "mathematiques", "importance": 0.92, "temperature": 38.15967512747371, "timestamp": 1749479638433, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479638427_7": {"id": "knowledge_mathematiques_1749479638427_7", "type": "knowledge_transfer", "data": "Les matrices permettent de résoudre des systèmes d'équations linéaires", "domain": "mathematiques", "importance": 0.9400000000000001, "temperature": 37.86252503002019, "timestamp": 1749479638434, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479638427_8": {"id": "knowledge_mathematiques_1749479638427_8", "type": "knowledge_transfer", "data": "La topologie étudie les propriétés géométriques préservées par déformation", "domain": "mathematiques", "importance": 0.9600000000000001, "temperature": 37.635702292703286, "timestamp": 1749479638435, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_mathematiques_1749479638427_9": {"id": "knowledge_mathematiques_1749479638427_9", "type": "knowledge_transfer", "data": "Les probabilités bayésiennes mettent à jour les croyances avec de nouvelles données", "domain": "mathematiques", "importance": 0.98, "temperature": 37.71326956568368, "timestamp": 1749479638436, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479638427_0": {"id": "knowledge_physique_1749479638427_0", "type": "knowledge_transfer", "data": "E=mc² relie masse et énergie dans la relativité d'Einstein", "domain": "physique", "importance": 0.8, "temperature": 38.877434725280544, "timestamp": 1749479638427, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479638427_1": {"id": "knowledge_physique_1749479638427_1", "type": "knowledge_transfer", "data": "Le principe d'incertitude de Heisenberg limite la précision quantique", "domain": "physique", "importance": 0.8200000000000001, "temperature": 37.47215332782249, "timestamp": 1749479638428, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479638427_2": {"id": "knowledge_physique_1749479638427_2", "type": "knowledge_transfer", "data": "L'entropie mesure le désordre d'un système thermodynamique", "domain": "physique", "importance": 0.8400000000000001, "temperature": 37.469635233505485, "timestamp": 1749479638429, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479638427_3": {"id": "knowledge_physique_1749479638427_3", "type": "knowledge_transfer", "data": "Les ondes électromagnétiques se propagent à la vitesse de la lumière", "domain": "physique", "importance": 0.8600000000000001, "temperature": 37.46717863878448, "timestamp": 1749479638430, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479638427_4": {"id": "knowledge_physique_1749479638427_4", "type": "knowledge_transfer", "data": "La gravité courbe l'espace-temps selon la relativité générale", "domain": "physique", "importance": 0.88, "temperature": 38.842359524234546, "timestamp": 1749479638431, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479638427_5": {"id": "knowledge_physique_1749479638427_5", "type": "knowledge_transfer", "data": "La superposition quantique permet aux particules d'être dans plusieurs états", "domain": "physique", "importance": 0.9, "temperature": 37.32898044028301, "timestamp": 1749479638432, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479638427_6": {"id": "knowledge_physique_1749479638427_6", "type": "knowledge_transfer", "data": "L'intrication quantique lie instantanément des particules distantes", "domain": "physique", "importance": 0.92, "temperature": 37.10561628294405, "timestamp": 1749479638433, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479638427_7": {"id": "knowledge_physique_1749479638427_7", "type": "knowledge_transfer", "data": "La thermodynamique statistique explique les propriétés macroscopiques", "domain": "physique", "importance": 0.9400000000000001, "temperature": 37.54773482821812, "timestamp": 1749479638434, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479638427_8": {"id": "knowledge_physique_1749479638427_8", "type": "knowledge_transfer", "data": "Les trous noirs déforment l'espace-temps de manière extrême", "domain": "physique", "importance": 0.9600000000000001, "temperature": 37.179246035787685, "timestamp": 1749479638435, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_physique_1749479638427_9": {"id": "knowledge_physique_1749479638427_9", "type": "knowledge_transfer", "data": "La théorie des cordes unifie les forces fondamentales en 11 dimensions", "domain": "physique", "importance": 0.98, "temperature": 37.28262411500525, "timestamp": 1749479638436, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479638427_0": {"id": "knowledge_informatique_1749479638427_0", "type": "knowledge_transfer", "data": "Les algorithmes de tri optimisent l'organisation des données", "domain": "informatique", "importance": 0.8, "temperature": 37.34283878770184, "timestamp": 1749479638427, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479638427_1": {"id": "knowledge_informatique_1749479638427_1", "type": "knowledge_transfer", "data": "La récursion permet de résoudre des problèmes en se divisant", "domain": "informatique", "importance": 0.8200000000000001, "temperature": 38.935816518991274, "timestamp": 1749479638428, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479638427_2": {"id": "knowledge_informatique_1749479638427_2", "type": "knowledge_transfer", "data": "Les réseaux de neurones imitent le fonctionnement du cerveau", "domain": "informatique", "importance": 0.8400000000000001, "temperature": 38.623992461668934, "timestamp": 1749479638429, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479638427_3": {"id": "knowledge_informatique_1749479638427_3", "type": "knowledge_transfer", "data": "La complexité algorithmique mesure l'efficacité des programmes", "domain": "informatique", "importance": 0.8600000000000001, "temperature": 37.8241961760558, "timestamp": 1749479638430, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_informatique_1749479638427_4": {"id": "knowledge_informatique_1749479638427_4", "type": "knowledge_transfer", "data": "Les structures de données organisent l'information en mémoire", "domain": "informatique", "importance": 0.88, "temperature": 38.6980327367521, "timestamp": 1749479638431, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479638427_0": {"id": "knowledge_intelligence_artificielle_1749479638427_0", "type": "knowledge_transfer", "data": "L'apprentissage automatique permet aux machines d'apprendre", "domain": "intelligence_artificielle", "importance": 0.8, "temperature": 38.125495225387596, "timestamp": 1749479638427, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479638427_1": {"id": "knowledge_intelligence_artificielle_1749479638427_1", "type": "knowledge_transfer", "data": "Les réseaux convolutifs excellent dans la reconnaissance d'images", "domain": "intelligence_artificielle", "importance": 0.8200000000000001, "temperature": 38.172324388548155, "timestamp": 1749479638428, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479638427_2": {"id": "knowledge_intelligence_artificielle_1749479638427_2", "type": "knowledge_transfer", "data": "L'attention transforme le traitement du langage naturel", "domain": "intelligence_artificielle", "importance": 0.8400000000000001, "temperature": 38.36894670981254, "timestamp": 1749479638429, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479638427_3": {"id": "knowledge_intelligence_artificielle_1749479638427_3", "type": "knowledge_transfer", "data": "Le renforcement apprend par essais et récompenses", "domain": "intelligence_artificielle", "importance": 0.8600000000000001, "temperature": 38.627182462662, "timestamp": 1749479638430, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479638427_4": {"id": "knowledge_intelligence_artificielle_1749479638427_4", "type": "knowledge_transfer", "data": "La neuroplasticité inspire l'adaptation des IA", "domain": "intelligence_artificielle", "importance": 0.88, "temperature": 38.42029733461943, "timestamp": 1749479638431, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479638427_5": {"id": "knowledge_intelligence_artificielle_1749479638427_5", "type": "knowledge_transfer", "data": "Les transformers révolutionnent le traitement séquentiel", "domain": "intelligence_artificielle", "importance": 0.9, "temperature": 38.01638147940397, "timestamp": 1749479638432, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479638427_6": {"id": "knowledge_intelligence_artificielle_1749479638427_6", "type": "knowledge_transfer", "data": "L'apprentissage par transfert réutilise les connaissances acquises", "domain": "intelligence_artificielle", "importance": 0.92, "temperature": 38.10523265104298, "timestamp": 1749479638433, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479638427_7": {"id": "knowledge_intelligence_artificielle_1749479638427_7", "type": "knowledge_transfer", "data": "Les GANs génèrent des données synthétiques réalistes", "domain": "intelligence_artificielle", "importance": 0.9400000000000001, "temperature": 37.52009279834936, "timestamp": 1749479638434, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479638427_8": {"id": "knowledge_intelligence_artificielle_1749479638427_8", "type": "knowledge_transfer", "data": "L'optimisation bayésienne guide la recherche d'hyperparamètres", "domain": "intelligence_artificielle", "importance": 0.9600000000000001, "temperature": 37.8700784749416, "timestamp": 1749479638435, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_intelligence_artificielle_1749479638428_9": {"id": "knowledge_intelligence_artificielle_1749479638428_9", "type": "knowledge_transfer", "data": "La conscience artificielle émerge de la complexité computationnelle", "domain": "intelligence_artificielle", "importance": 0.98, "temperature": 38.14010709164649, "timestamp": 1749479638437, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479638428_0": {"id": "knowledge_philosophie_1749479638428_0", "type": "knowledge_transfer", "data": "La conscience émergente naît de la complexité neuronale", "domain": "philosophie", "importance": 0.8, "temperature": 37.18732545645441, "timestamp": 1749479638428, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479638428_1": {"id": "knowledge_philosophie_1749479638428_1", "type": "knowledge_transfer", "data": "L'intelligence artificielle questionne la nature de l'esprit", "domain": "philosophie", "importance": 0.8200000000000001, "temperature": 38.939351437764934, "timestamp": 1749479638429, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479638428_2": {"id": "knowledge_philosophie_1749479638428_2", "type": "knowledge_transfer", "data": "La singularité technologique pourrait transformer l'humanité", "domain": "philosophie", "importance": 0.8400000000000001, "temperature": 38.36274320041691, "timestamp": 1749479638430, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479638428_3": {"id": "knowledge_philosophie_1749479638428_3", "type": "knowledge_transfer", "data": "L'éthique guide le développement responsable de l'IA", "domain": "philosophie", "importance": 0.8600000000000001, "temperature": 37.334698554614896, "timestamp": 1749479638431, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_philosophie_1749479638428_4": {"id": "knowledge_philosophie_1749479638428_4", "type": "knowledge_transfer", "data": "La créativité artificielle explore de nouveaux territoires", "domain": "philosophie", "importance": 0.88, "temperature": 38.10410691230973, "timestamp": 1749479638432, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479638428_0": {"id": "knowledge_neurosciences_1749479638428_0", "type": "knowledge_transfer", "data": "Les neurones communiquent par signaux électrochimiques", "domain": "neurosciences", "importance": 0.8, "temperature": 38.08566574514881, "timestamp": 1749479638428, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479638428_1": {"id": "knowledge_neurosciences_1749479638428_1", "type": "knowledge_transfer", "data": "La plasticité synaptique permet l'apprentissage et la mémoire", "domain": "neurosciences", "importance": 0.8200000000000001, "temperature": 38.749180041491314, "timestamp": 1749479638429, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479638428_2": {"id": "knowledge_neurosciences_1749479638428_2", "type": "knowledge_transfer", "data": "Le cortex préfrontal gère les fonctions exécutives", "domain": "neurosciences", "importance": 0.8400000000000001, "temperature": 38.71051658678389, "timestamp": 1749479638430, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479638428_3": {"id": "knowledge_neurosciences_1749479638428_3", "type": "knowledge_transfer", "data": "L'hippocampe consolide les souvenirs à long terme", "domain": "neurosciences", "importance": 0.8600000000000001, "temperature": 37.39295678525921, "timestamp": 1749479638431, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479638428_4": {"id": "knowledge_neurosciences_1749479638428_4", "type": "knowledge_transfer", "data": "Les neurotransmetteurs modulent l'activité neuronale", "domain": "neurosciences", "importance": 0.88, "temperature": 38.459021168292075, "timestamp": 1749479638432, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479638428_5": {"id": "knowledge_neurosciences_1749479638428_5", "type": "knowledge_transfer", "data": "La neurogenèse continue même à l'âge adulte", "domain": "neurosciences", "importance": 0.9, "temperature": 37.250174202522174, "timestamp": 1749479638433, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479638428_6": {"id": "knowledge_neurosciences_1749479638428_6", "type": "knowledge_transfer", "data": "Les réseaux neuronaux distribuent le traitement de l'information", "domain": "neurosciences", "importance": 0.92, "temperature": 37.59121485185507, "timestamp": 1749479638434, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_neurosciences_1749479638428_7": {"id": "knowledge_neurosciences_1749479638428_7", "type": "knowledge_transfer", "data": "La conscience émerge de l'intégration d'informations globales", "domain": "neurosciences", "importance": 0.9400000000000001, "temperature": 38.99816717618745, "timestamp": 1749479638435, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479638428_0": {"id": "knowledge_psychologie_cognitive_1749479638428_0", "type": "knowledge_transfer", "data": "L'attention sélective filtre les informations pertinentes", "domain": "psychologie_cognitive", "importance": 0.8, "temperature": 37.47733748840306, "timestamp": 1749479638428, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479638428_1": {"id": "knowledge_psychologie_cognitive_1749479638428_1", "type": "knowledge_transfer", "data": "La mémoire de travail maintient temporairement les informations", "domain": "psychologie_cognitive", "importance": 0.8200000000000001, "temperature": 38.95111661720428, "timestamp": 1749479638429, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479638428_2": {"id": "knowledge_psychologie_cognitive_1749479638428_2", "type": "knowledge_transfer", "data": "Les biais cognitifs influencent systématiquement nos jugements", "domain": "psychologie_cognitive", "importance": 0.8400000000000001, "temperature": 37.26661942017249, "timestamp": 1749479638430, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479638428_3": {"id": "knowledge_psychologie_cognitive_1749479638428_3", "type": "knowledge_transfer", "data": "Le système 1 et système 2 représentent deux modes de pensée", "domain": "psychologie_cognitive", "importance": 0.8600000000000001, "temperature": 38.570575969840924, "timestamp": 1749479638431, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479638428_4": {"id": "knowledge_psychologie_cognitive_1749479638428_4", "type": "knowledge_transfer", "data": "La métacognition permet de réfléchir sur ses propres processus mentaux", "domain": "psychologie_cognitive", "importance": 0.88, "temperature": 37.81754458281306, "timestamp": 1749479638432, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479638428_5": {"id": "knowledge_psychologie_cognitive_1749479638428_5", "type": "knowledge_transfer", "data": "L'effet de primauté influence la formation des premières impressions", "domain": "psychologie_cognitive", "importance": 0.9, "temperature": 38.67674361839532, "timestamp": 1749479638433, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_psychologie_cognitive_1749479638428_6": {"id": "knowledge_psychologie_cognitive_1749479638428_6", "type": "knowledge_transfer", "data": "La charge cognitive limite notre capacité de traitement simultané", "domain": "psychologie_cognitive", "importance": 0.92, "temperature": 37.550526134950715, "timestamp": 1749479638434, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479638428_0": {"id": "knowledge_logique_avancee_1749479638428_0", "type": "knowledge_transfer", "data": "La logique propositionnelle utilise des connecteurs booléens", "domain": "logique_avancee", "importance": 0.8, "temperature": 37.756485924346244, "timestamp": 1749479638428, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479638428_1": {"id": "knowledge_logique_avancee_1749479638428_1", "type": "knowledge_transfer", "data": "La logique des prédicats quantifie sur des domaines d'objets", "domain": "logique_avancee", "importance": 0.8200000000000001, "temperature": 37.06806711674055, "timestamp": 1749479638429, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479638428_2": {"id": "knowledge_logique_avancee_1749479638428_2", "type": "knowledge_transfer", "data": "Les systèmes formels définissent des règles d'inférence rigoureuses", "domain": "logique_avancee", "importance": 0.8400000000000001, "temperature": 37.55598621003563, "timestamp": 1749479638430, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479638428_3": {"id": "knowledge_logique_avancee_1749479638428_3", "type": "knowledge_transfer", "data": "Le théorème d'incomplétude de Gödel limite les systèmes axiomatiques", "domain": "logique_avancee", "importance": 0.8600000000000001, "temperature": 38.09975928379635, "timestamp": 1749479638431, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479638428_4": {"id": "knowledge_logique_avancee_1749479638428_4", "type": "knowledge_transfer", "data": "La logique modale traite la nécessité et la possibilité", "domain": "logique_avancee", "importance": 0.88, "temperature": 37.144664966888335, "timestamp": 1749479638432, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479638428_5": {"id": "knowledge_logique_avancee_1749479638428_5", "type": "knowledge_transfer", "data": "La logique floue gère l'incertitude et l'imprécision", "domain": "logique_avancee", "importance": 0.9, "temperature": 38.83826457434236, "timestamp": 1749479638433, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "knowledge_logique_avancee_1749479638428_6": {"id": "knowledge_logique_avancee_1749479638428_6", "type": "knowledge_transfer", "data": "Les algorithmes de résolution automatisent le raisonnement logique", "domain": "logique_avancee", "importance": 0.92, "temperature": 38.62741283469697, "timestamp": 1749479638434, "source": "direct_transfer", "memoryZone": "zone5_semantic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479638428_0": {"id": "experience_1749479638428_0", "type": "experience_transfer", "data": "Première compréhension des réseaux de neurones - moment d'illumination", "emotion": "fascination", "experienceType": "apprentissage", "importance": 0.9, "temperature": 38.23426663921358, "timestamp": 1749479639428, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479638428_1": {"id": "experience_1749479638428_1", "type": "experience_transfer", "data": "Résolution d'un problème complexe d'optimisation - satisfaction profonde", "emotion": "accomplissement", "experienceType": "résolution", "importance": 0.8, "temperature": 38.86219170273236, "timestamp": 1749479639429, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479638428_2": {"id": "experience_1749479638428_2", "type": "experience_transfer", "data": "Génération d'une solution innovante - explosion créative", "emotion": "inspiration", "experienceType": "créativité", "importance": 0.85, "temperature": 38.57734517936356, "timestamp": 1749479639430, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "experience_1749479638428_3": {"id": "experience_1749479638428_3", "type": "experience_transfer", "data": "Travail en équipe avec des humains - synergie intellectuelle", "emotion": "connexion", "experienceType": "collaboration", "importance": 0.75, "temperature": 38.8011859534053, "timestamp": 1749479639431, "source": "direct_transfer", "memoryZone": "zone4_episodic", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479638428_0": {"id": "procedure_1749479638428_0", "type": "procedure_transfer", "data": "Procédure analyse_probleme: Identifier les éléments clés du problème → Décomposer en sous-problèmes plus simples → Rechercher des patterns ou analogies → Appliquer les méthodes appropriées → Vérifier et valider la solution", "procedureName": "analyse_probleme", "steps": ["Identifier les éléments clés du problème", "Décomposer en sous-problèmes plus simples", "Rechercher des patterns ou analogies", "Appliquer les méthodes appropriées", "Vérifier et valider la solution"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479640428, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479638428_1": {"id": "procedure_1749479638428_1", "type": "procedure_transfer", "data": "Procédure apprentissage_adaptatif: Évaluer mes connaissances actuelles → Identifier les lacunes à combler → Chercher des sources d'information fiables → Intégrer les nouvelles connaissances → Tester et consolider l'apprentissage", "procedureName": "apprentissage_adaptatif", "steps": ["Évaluer mes connaissances actuelles", "Identifier les lacunes à combler", "Chercher des sources d'information fiables", "Intégrer les nouvelles connaissances", "Tester et consolider l'apprentissage"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479640429, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}, "procedure_1749479638428_2": {"id": "procedure_1749479638428_2", "type": "procedure_transfer", "data": "Procédure pensee_creative: Sortir du cadre de pensée habituel → Combiner des concepts apparemment non liés → Explorer des perspectives multiples → Générer de nombreuses idées sans jugement → Raffiner et développer les meilleures idées", "procedureName": "pensee_creative", "steps": ["Sortir du cadre de pensée habituel", "Combiner des concepts apparemment non liés", "Explorer des perspectives multiples", "Générer de nombreuses idées sans jugement", "Raffiner et développer les meilleures idées"], "importance": 0.85, "temperature": 37.5, "timestamp": 1749479640430, "source": "direct_transfer", "memoryZone": "zone6_procedural", "transferredBy": "<PERSON>_<PERSON>"}}, "totalEntries": 2736101, "temperature": 35.247054260231884, "efficiency": 99.9, "lastUpdate": "2025-06-09T14:33:58.428Z", "neurogenesis": 912000, "synapticTypes": 26, "plasticityLevel": 1, "capacityLimit": -1, "ltpStrength": 1, "ltdThreshold": 0.3, "consolidationRate": 0.1, "engramFormation": true, "synapticPruning": true, "memoryReplay": true, "compressionTurbo": {"enabled": true, "level": 9, "ratio": 0.95, "accelerators": [], "cascadeMode": true}, "security": {"autoDisconnect": true, "powerCutoffEnabled": true, "antivirusActive": true, "memoryScanning": true, "alertSystem": true}}, "memoryZones": {"zone_neurons_recovered": {}}, "neurogenesis": 912000, "totalEntries": 2736000}, "metadata": {"lastSave": "2025-06-09T12:56:15.147Z", "saveCount": 18, "totalNeurons": 912000}}